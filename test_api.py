#!/usr/bin/env python3
"""
测试数据明细API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models import User, PlatformAccount

def test_database():
    """测试数据库连接和数据"""
    db = SessionLocal()
    try:
        # 检查用户
        users = db.query(User).all()
        print('用户列表:')
        for user in users:
            print(f'  ID: {user.id}, 用户名: {user.username}, 邮箱: {user.email}')

        # 检查账号
        accounts = db.query(PlatformAccount).all()
        print('\n账号列表:')
        for account in accounts:
            print(f'  ID: {account.id}, 名称: {account.name}, 平台: {account.platform}, 用户ID: {account.user_id}')
            
        # 检查微信公众号账号
        wechat_accounts = db.query(PlatformAccount).filter(
            PlatformAccount.platform == "wechat_mp"
        ).all()
        print('\n微信公众号账号:')
        for account in wechat_accounts:
            print(f'  ID: {account.id}, 名称: {account.name}, 用户ID: {account.user_id}')
            
    finally:
        db.close()

if __name__ == "__main__":
    test_database()
