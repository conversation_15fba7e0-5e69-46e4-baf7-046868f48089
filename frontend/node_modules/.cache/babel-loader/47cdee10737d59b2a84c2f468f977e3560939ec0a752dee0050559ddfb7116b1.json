{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider,\n  Content\n} = Layout;\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst DataDetails = () => {\n  _s();\n  const [selectedPlatform, setSelectedPlatform] = useState('wechat_mp');\n  const [selectedDataType, setSelectedDataType] = useState('content_trend');\n  const [selectedAccount, setSelectedAccount] = useState(null);\n  const [accounts, setAccounts] = useState([]);\n  const [dataList, setDataList] = useState([]);\n  const [dataConfig, setDataConfig] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 20,\n    total: 0\n  });\n  const [searchText, setSearchText] = useState('');\n\n  // 平台配置\n  const platformConfig = {\n    wechat_mp: {\n      name: '微信公众号',\n      dataTypes: {\n        content_trend: '内容数据趋势明细',\n        content_source: '内容流量来源明细',\n        content_detail: '内容已通知内容明细',\n        user_channel: '用户增长明细'\n      }\n    },\n    wechat_channels: {\n      name: '视频号',\n      dataTypes: {}\n    },\n    xiaohongshu: {\n      name: '小红书',\n      dataTypes: {}\n    }\n  };\n\n  // 菜单项\n  const menuItems = [{\n    key: 'wechat_mp',\n    label: '微信公众号',\n    children: [{\n      key: 'wechat_mp_content_trend',\n      label: '内容数据趋势明细'\n    }, {\n      key: 'wechat_mp_content_source',\n      label: '内容流量来源明细'\n    }, {\n      key: 'wechat_mp_content_detail',\n      label: '内容已通知内容明细'\n    }, {\n      key: 'wechat_mp_user_channel',\n      label: '用户增长明细'\n    }]\n  }, {\n    key: 'wechat_channels',\n    label: '视频号',\n    disabled: true,\n    children: [{\n      key: 'wechat_channels_placeholder',\n      label: '敬请期待',\n      disabled: true\n    }]\n  }, {\n    key: 'xiaohongshu',\n    label: '小红书',\n    disabled: true,\n    children: [{\n      key: 'xiaohongshu_placeholder',\n      label: '敬请期待',\n      disabled: true\n    }]\n  }];\n  useEffect(() => {\n    fetchDataConfig();\n    fetchAccounts();\n  }, []);\n  useEffect(() => {\n    if (selectedAccount && selectedDataType) {\n      fetchDataList();\n    }\n  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);\n  const fetchDataConfig = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/config');\n      if (response.data.success) {\n        setDataConfig(response.data.data_types);\n      }\n    } catch (error) {\n      message.error('获取数据配置失败');\n    }\n  };\n  const fetchAccounts = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/accounts');\n      if (response.data.success) {\n        setAccounts(response.data.accounts);\n        if (response.data.accounts.length > 0) {\n          setSelectedAccount(response.data.accounts[0].id);\n        }\n      }\n    } catch (error) {\n      message.error('获取账号列表失败');\n    }\n  };\n  const fetchDataList = async () => {\n    if (!selectedAccount || !selectedDataType) return;\n    setLoading(true);\n    try {\n      const params = {\n        account_id: selectedAccount,\n        page: pagination.current,\n        page_size: pagination.pageSize,\n        search: searchText || undefined,\n        sort_field: 'created_at',\n        sort_order: 'desc'\n      };\n      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, {\n        params\n      });\n      if (response.data.success) {\n        setDataList(response.data.data);\n        setPagination(prev => ({\n          ...prev,\n          total: response.data.total,\n          current: response.data.page\n        }));\n      } else {\n        message.error(response.data.error || '获取数据失败');\n      }\n    } catch (error) {\n      message.error('获取数据列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMenuClick = ({\n    key\n  }) => {\n    const parts = key.split('_');\n    if (parts.length >= 3) {\n      const platform = parts.slice(0, 2).join('_'); // wechat_mp\n      const dataType = parts.slice(2).join('_'); // content_trend\n\n      setSelectedPlatform(platform);\n      setSelectedDataType(dataType);\n      setPagination(prev => ({\n        ...prev,\n        current: 1\n      }));\n    }\n  };\n  const handleTableChange = paginationInfo => {\n    setPagination(prev => ({\n      ...prev,\n      current: paginationInfo.current,\n      pageSize: paginationInfo.pageSize\n    }));\n  };\n  const handleSearch = value => {\n    setSearchText(value);\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleRefresh = () => {\n    fetchDataList();\n  };\n\n  // 生成表格列配置\n  const generateColumns = () => {\n    const config = dataConfig[selectedDataType];\n    if (!config) return [];\n    return config.columns.map(col => ({\n      title: col.title,\n      dataIndex: col.key,\n      key: col.key,\n      render: value => {\n        if (col.type === 'date' || col.type === 'datetime') {\n          return value ? new Date(value).toLocaleString() : '-';\n        }\n        if (col.type === 'number') {\n          return typeof value === 'number' ? value.toLocaleString() : value || 0;\n        }\n        if (col.type === 'url') {\n          return value ? /*#__PURE__*/_jsxDEV(\"a\", {\n            href: value,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"\\u67E5\\u770B\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this) : '-';\n        }\n        return value || '-';\n      },\n      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',\n      width: col.type === 'text' ? 200 : 120\n    }));\n  };\n  const currentConfig = dataConfig[selectedDataType];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh'\n    },\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        height: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        width: 250,\n        theme: \"light\",\n        style: {\n          borderRight: '1px solid #f0f0f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #f0f0f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u6570\\u636E\\u660E\\u7EC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          mode: \"inline\",\n          selectedKeys: [`${selectedPlatform}_${selectedDataType}`],\n          items: menuItems,\n          onClick: handleMenuClick,\n          style: {\n            height: 'calc(100% - 64px)',\n            borderRight: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Content, {\n          style: {\n            padding: '24px',\n            background: '#fff'\n          },\n          children: [currentConfig && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: currentConfig.name,\n              extra: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: selectedAccount,\n                  onChange: setSelectedAccount,\n                  style: {\n                    width: 200\n                  },\n                  placeholder: \"\\u9009\\u62E9\\u8D26\\u53F7\",\n                  children: accounts.map(account => /*#__PURE__*/_jsxDEV(Option, {\n                    value: account.id,\n                    children: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [account.name, /*#__PURE__*/_jsxDEV(Tag, {\n                        color: account.login_status ? 'green' : 'red',\n                        children: account.login_status ? '已登录' : '未登录'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 29\n                    }, this)\n                  }, account.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Search, {\n                  placeholder: \"\\u641C\\u7D22...\",\n                  allowClear: true,\n                  onSearch: handleSearch,\n                  style: {\n                    width: 200\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 37\n                  }, this),\n                  onClick: handleRefresh,\n                  children: \"\\u5237\\u65B0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this),\n              style: {\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  color: '#666'\n                },\n                children: currentConfig.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table, {\n              columns: generateColumns(),\n              dataSource: dataList,\n              rowKey: \"id\",\n              loading: loading,\n              pagination: {\n                ...pagination,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`\n              },\n              onChange: handleTableChange,\n              scroll: {\n                x: 'max-content'\n              },\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !currentConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Spin, {\n              size: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginTop: 16\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 5\n  }, this);\n};\n_s(DataDetails, \"Lhs7VRBVmJ3RWpZWKp4WrMVlhTk=\");\n_c = DataDetails;\nexport default DataDetails;\nvar _c;\n$RefreshReg$(_c, \"DataDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "<PERSON><PERSON>", "Table", "Card", "Select", "Input", "<PERSON><PERSON>", "message", "Spin", "Tag", "Space", "ReloadOutlined", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Content", "Search", "Option", "DataDetails", "_s", "selectedPlatform", "setSelectedPlatform", "selectedDataType", "setSelectedDataType", "selectedAccount", "setSelectedAccount", "accounts", "setAccounts", "dataList", "setDataList", "dataConfig", "setDataConfig", "loading", "setLoading", "pagination", "setPagination", "current", "pageSize", "total", "searchText", "setSearchText", "platformConfig", "wechat_mp", "name", "dataTypes", "content_trend", "content_source", "content_detail", "user_channel", "wechat_channels", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menuItems", "key", "label", "children", "disabled", "fetchDataConfig", "fetchAccounts", "fetchDataList", "response", "get", "data", "success", "data_types", "error", "length", "id", "params", "account_id", "page", "page_size", "search", "undefined", "sort_field", "sort_order", "prev", "handleMenuClick", "parts", "split", "platform", "slice", "join", "dataType", "handleTableChange", "paginationInfo", "handleSearch", "value", "handleRefresh", "generateColumns", "config", "columns", "map", "col", "title", "dataIndex", "render", "type", "Date", "toLocaleString", "href", "target", "rel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sorter", "width", "currentConfig", "style", "height", "theme", "borderRight", "padding", "borderBottom", "mode", "<PERSON><PERSON><PERSON><PERSON>", "items", "onClick", "background", "extra", "onChange", "placeholder", "account", "color", "login_status", "allowClear", "onSearch", "icon", "marginBottom", "margin", "description", "dataSource", "<PERSON><PERSON><PERSON>", "showSizeChanger", "showQuickJumper", "showTotal", "range", "scroll", "x", "size", "textAlign", "marginTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';\nimport { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';\nimport api from '../services/api';\n\nconst { Sider, Content } = Layout;\nconst { Search } = Input;\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n}\n\ninterface DataItem {\n  id: number;\n  account_id: number;\n  [key: string]: any;\n}\n\ninterface DataConfig {\n  name: string;\n  description: string;\n  columns: Array<{\n    key: string;\n    title: string;\n    type: string;\n  }>;\n}\n\ninterface DataTypeConfig {\n  [key: string]: DataConfig;\n}\n\nconst DataDetails: React.FC = () => {\n  const [selectedPlatform, setSelectedPlatform] = useState<string>('wechat_mp');\n  const [selectedDataType, setSelectedDataType] = useState<string>('content_trend');\n  const [selectedAccount, setSelectedAccount] = useState<number | null>(null);\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [dataList, setDataList] = useState<DataItem[]>([]);\n  const [dataConfig, setDataConfig] = useState<DataTypeConfig>({});\n  const [loading, setLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 20,\n    total: 0,\n  });\n  const [searchText, setSearchText] = useState<string>('');\n\n  // 平台配置\n  const platformConfig = {\n    wechat_mp: {\n      name: '微信公众号',\n      dataTypes: {\n        content_trend: '内容数据趋势明细',\n        content_source: '内容流量来源明细',\n        content_detail: '内容已通知内容明细',\n        user_channel: '用户增长明细'\n      }\n    },\n    wechat_channels: {\n      name: '视频号',\n      dataTypes: {}\n    },\n    xiaohongshu: {\n      name: '小红书',\n      dataTypes: {}\n    }\n  };\n\n  // 菜单项\n  const menuItems = [\n    {\n      key: 'wechat_mp',\n      label: '微信公众号',\n      children: [\n        { key: 'wechat_mp_content_trend', label: '内容数据趋势明细' },\n        { key: 'wechat_mp_content_source', label: '内容流量来源明细' },\n        { key: 'wechat_mp_content_detail', label: '内容已通知内容明细' },\n        { key: 'wechat_mp_user_channel', label: '用户增长明细' }\n      ]\n    },\n    {\n      key: 'wechat_channels',\n      label: '视频号',\n      disabled: true,\n      children: [\n        { key: 'wechat_channels_placeholder', label: '敬请期待', disabled: true }\n      ]\n    },\n    {\n      key: 'xiaohongshu',\n      label: '小红书',\n      disabled: true,\n      children: [\n        { key: 'xiaohongshu_placeholder', label: '敬请期待', disabled: true }\n      ]\n    }\n  ];\n\n  useEffect(() => {\n    fetchDataConfig();\n    fetchAccounts();\n  }, []);\n\n  useEffect(() => {\n    if (selectedAccount && selectedDataType) {\n      fetchDataList();\n    }\n  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);\n\n  const fetchDataConfig = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/config');\n      if (response.data.success) {\n        setDataConfig(response.data.data_types);\n      }\n    } catch (error) {\n      message.error('获取数据配置失败');\n    }\n  };\n\n  const fetchAccounts = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/accounts');\n      if (response.data.success) {\n        setAccounts(response.data.accounts);\n        if (response.data.accounts.length > 0) {\n          setSelectedAccount(response.data.accounts[0].id);\n        }\n      }\n    } catch (error) {\n      message.error('获取账号列表失败');\n    }\n  };\n\n  const fetchDataList = async () => {\n    if (!selectedAccount || !selectedDataType) return;\n\n    setLoading(true);\n    try {\n      const params = {\n        account_id: selectedAccount,\n        page: pagination.current,\n        page_size: pagination.pageSize,\n        search: searchText || undefined,\n        sort_field: 'created_at',\n        sort_order: 'desc'\n      };\n\n      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, { params });\n      \n      if (response.data.success) {\n        setDataList(response.data.data);\n        setPagination(prev => ({\n          ...prev,\n          total: response.data.total,\n          current: response.data.page\n        }));\n      } else {\n        message.error(response.data.error || '获取数据失败');\n      }\n    } catch (error) {\n      message.error('获取数据列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    const parts = key.split('_');\n    if (parts.length >= 3) {\n      const platform = parts.slice(0, 2).join('_'); // wechat_mp\n      const dataType = parts.slice(2).join('_'); // content_trend\n      \n      setSelectedPlatform(platform);\n      setSelectedDataType(dataType);\n      setPagination(prev => ({ ...prev, current: 1 }));\n    }\n  };\n\n  const handleTableChange = (paginationInfo: any) => {\n    setPagination(prev => ({\n      ...prev,\n      current: paginationInfo.current,\n      pageSize: paginationInfo.pageSize\n    }));\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchText(value);\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handleRefresh = () => {\n    fetchDataList();\n  };\n\n  // 生成表格列配置\n  const generateColumns = () => {\n    const config = dataConfig[selectedDataType];\n    if (!config) return [];\n\n    return config.columns.map(col => ({\n      title: col.title,\n      dataIndex: col.key,\n      key: col.key,\n      render: (value: any) => {\n        if (col.type === 'date' || col.type === 'datetime') {\n          return value ? new Date(value).toLocaleString() : '-';\n        }\n        if (col.type === 'number') {\n          return typeof value === 'number' ? value.toLocaleString() : value || 0;\n        }\n        if (col.type === 'url') {\n          return value ? (\n            <a href={value} target=\"_blank\" rel=\"noopener noreferrer\">\n              查看链接\n            </a>\n          ) : '-';\n        }\n        return value || '-';\n      },\n      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',\n      width: col.type === 'text' ? 200 : 120\n    }));\n  };\n\n  const currentConfig = dataConfig[selectedDataType];\n\n  return (\n    <div style={{ height: '100vh' }}>\n      <Layout style={{ height: '100%' }}>\n        <Sider width={250} theme=\"light\" style={{ borderRight: '1px solid #f0f0f0' }}>\n          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>\n            <h3>数据明细</h3>\n          </div>\n          <Menu\n            mode=\"inline\"\n            selectedKeys={[`${selectedPlatform}_${selectedDataType}`]}\n            items={menuItems}\n            onClick={handleMenuClick}\n            style={{ height: 'calc(100% - 64px)', borderRight: 0 }}\n          />\n        </Sider>\n        \n        <Layout>\n          <Content style={{ padding: '24px', background: '#fff' }}>\n            {currentConfig && (\n              <>\n                <Card \n                  title={currentConfig.name}\n                  extra={\n                    <Space>\n                      <Select\n                        value={selectedAccount}\n                        onChange={setSelectedAccount}\n                        style={{ width: 200 }}\n                        placeholder=\"选择账号\"\n                      >\n                        {accounts.map(account => (\n                          <Option key={account.id} value={account.id}>\n                            <Space>\n                              {account.name}\n                              <Tag color={account.login_status ? 'green' : 'red'}>\n                                {account.login_status ? '已登录' : '未登录'}\n                              </Tag>\n                            </Space>\n                          </Option>\n                        ))}\n                      </Select>\n                      <Search\n                        placeholder=\"搜索...\"\n                        allowClear\n                        onSearch={handleSearch}\n                        style={{ width: 200 }}\n                      />\n                      <Button icon={<ReloadOutlined />} onClick={handleRefresh}>\n                        刷新\n                      </Button>\n                    </Space>\n                  }\n                  style={{ marginBottom: 16 }}\n                >\n                  <p style={{ margin: 0, color: '#666' }}>{currentConfig.description}</p>\n                </Card>\n\n                <Table\n                  columns={generateColumns()}\n                  dataSource={dataList}\n                  rowKey=\"id\"\n                  loading={loading}\n                  pagination={{\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range) => \n                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`\n                  }}\n                  onChange={handleTableChange}\n                  scroll={{ x: 'max-content' }}\n                  size=\"small\"\n                />\n              </>\n            )}\n            \n            {!currentConfig && (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin size=\"large\" />\n                <p style={{ marginTop: 16 }}>加载中...</p>\n              </div>\n            )}\n          </Content>\n        </Layout>\n      </Layout>\n    </div>\n  );\n};\n\nexport default DataDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AAClG,SAAyBC,cAAc,QAA0B,mBAAmB;AACpF,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGlB,MAAM;AACjC,MAAM;EAAEmB;AAAO,CAAC,GAAGd,KAAK;AACxB,MAAM;EAAEe;AAAO,CAAC,GAAGhB,MAAM;AA8BzB,MAAMiB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAS,WAAW,CAAC;EAC7E,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAS,eAAe,CAAC;EACjF,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAa,EAAE,CAAC;EACxD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAiB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC;IAC3CyC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAS,EAAE,CAAC;;EAExD;EACA,MAAM8C,cAAc,GAAG;IACrBC,SAAS,EAAE;MACTC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE;QACTC,aAAa,EAAE,UAAU;QACzBC,cAAc,EAAE,UAAU;QAC1BC,cAAc,EAAE,WAAW;QAC3BC,YAAY,EAAE;MAChB;IACF,CAAC;IACDC,eAAe,EAAE;MACfN,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,CAAC;IACd,CAAC;IACDM,WAAW,EAAE;MACXP,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMO,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,yBAAyB;MAAEC,KAAK,EAAE;IAAW,CAAC,EACrD;MAAED,GAAG,EAAE,0BAA0B;MAAEC,KAAK,EAAE;IAAW,CAAC,EACtD;MAAED,GAAG,EAAE,0BAA0B;MAAEC,KAAK,EAAE;IAAY,CAAC,EACvD;MAAED,GAAG,EAAE,wBAAwB;MAAEC,KAAK,EAAE;IAAS,CAAC;EAEtD,CAAC,EACD;IACED,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,KAAK;IACZE,QAAQ,EAAE,IAAI;IACdD,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,6BAA6B;MAAEC,KAAK,EAAE,MAAM;MAAEE,QAAQ,EAAE;IAAK,CAAC;EAEzE,CAAC,EACD;IACEH,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,KAAK;IACZE,QAAQ,EAAE,IAAI;IACdD,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,yBAAyB;MAAEC,KAAK,EAAE,MAAM;MAAEE,QAAQ,EAAE;IAAK,CAAC;EAErE,CAAC,CACF;EAED3D,SAAS,CAAC,MAAM;IACd4D,eAAe,CAAC,CAAC;IACjBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN7D,SAAS,CAAC,MAAM;IACd,IAAI4B,eAAe,IAAIF,gBAAgB,EAAE;MACvCoC,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAClC,eAAe,EAAEF,gBAAgB,EAAEY,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,EAAEE,UAAU,CAAC,CAAC;EAE5F,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMlD,GAAG,CAACmD,GAAG,CAAC,gCAAgC,CAAC;MAChE,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB/B,aAAa,CAAC4B,QAAQ,CAACE,IAAI,CAACE,UAAU,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMP,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMlD,GAAG,CAACmD,GAAG,CAAC,kCAAkC,CAAC;MAClE,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBnC,WAAW,CAACgC,QAAQ,CAACE,IAAI,CAACnC,QAAQ,CAAC;QACnC,IAAIiC,QAAQ,CAACE,IAAI,CAACnC,QAAQ,CAACuC,MAAM,GAAG,CAAC,EAAE;UACrCxC,kBAAkB,CAACkC,QAAQ,CAACE,IAAI,CAACnC,QAAQ,CAAC,CAAC,CAAC,CAACwC,EAAE,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMN,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAClC,eAAe,IAAI,CAACF,gBAAgB,EAAE;IAE3CW,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkC,MAAM,GAAG;QACbC,UAAU,EAAE5C,eAAe;QAC3B6C,IAAI,EAAEnC,UAAU,CAACE,OAAO;QACxBkC,SAAS,EAAEpC,UAAU,CAACG,QAAQ;QAC9BkC,MAAM,EAAEhC,UAAU,IAAIiC,SAAS;QAC/BC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE;MACd,CAAC;MAED,MAAMf,QAAQ,GAAG,MAAMlD,GAAG,CAACmD,GAAG,CAAC,2BAA2BtC,gBAAgB,EAAE,EAAE;QAAE6C;MAAO,CAAC,CAAC;MAEzF,IAAIR,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBjC,WAAW,CAAC8B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;QAC/B1B,aAAa,CAACwC,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPrC,KAAK,EAAEqB,QAAQ,CAACE,IAAI,CAACvB,KAAK;UAC1BF,OAAO,EAAEuB,QAAQ,CAACE,IAAI,CAACQ;QACzB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLjE,OAAO,CAAC4D,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACG,KAAK,IAAI,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,eAAe,GAAGA,CAAC;IAAExB;EAAqB,CAAC,KAAK;IACpD,MAAMyB,KAAK,GAAGzB,GAAG,CAAC0B,KAAK,CAAC,GAAG,CAAC;IAC5B,IAAID,KAAK,CAACZ,MAAM,IAAI,CAAC,EAAE;MACrB,MAAMc,QAAQ,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9C,MAAMC,QAAQ,GAAGL,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE3C5D,mBAAmB,CAAC0D,QAAQ,CAAC;MAC7BxD,mBAAmB,CAAC2D,QAAQ,CAAC;MAC7B/C,aAAa,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEvC,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAM+C,iBAAiB,GAAIC,cAAmB,IAAK;IACjDjD,aAAa,CAACwC,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPvC,OAAO,EAAEgD,cAAc,CAAChD,OAAO;MAC/BC,QAAQ,EAAE+C,cAAc,CAAC/C;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgD,YAAY,GAAIC,KAAa,IAAK;IACtC9C,aAAa,CAAC8C,KAAK,CAAC;IACpBnD,aAAa,CAACwC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvC,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMmD,aAAa,GAAGA,CAAA,KAAM;IAC1B7B,aAAa,CAAC,CAAC;EACjB,CAAC;;EAED;EACA,MAAM8B,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,MAAM,GAAG3D,UAAU,CAACR,gBAAgB,CAAC;IAC3C,IAAI,CAACmE,MAAM,EAAE,OAAO,EAAE;IAEtB,OAAOA,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,GAAG,KAAK;MAChCC,KAAK,EAAED,GAAG,CAACC,KAAK;MAChBC,SAAS,EAAEF,GAAG,CAACxC,GAAG;MAClBA,GAAG,EAAEwC,GAAG,CAACxC,GAAG;MACZ2C,MAAM,EAAGT,KAAU,IAAK;QACtB,IAAIM,GAAG,CAACI,IAAI,KAAK,MAAM,IAAIJ,GAAG,CAACI,IAAI,KAAK,UAAU,EAAE;UAClD,OAAOV,KAAK,GAAG,IAAIW,IAAI,CAACX,KAAK,CAAC,CAACY,cAAc,CAAC,CAAC,GAAG,GAAG;QACvD;QACA,IAAIN,GAAG,CAACI,IAAI,KAAK,QAAQ,EAAE;UACzB,OAAO,OAAOV,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACY,cAAc,CAAC,CAAC,GAAGZ,KAAK,IAAI,CAAC;QACxE;QACA,IAAIM,GAAG,CAACI,IAAI,KAAK,KAAK,EAAE;UACtB,OAAOV,KAAK,gBACV3E,OAAA;YAAGwF,IAAI,EAAEb,KAAM;YAACc,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAA/C,QAAA,EAAC;UAE1D;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GACF,GAAG;QACT;QACA,OAAOnB,KAAK,IAAI,GAAG;MACrB,CAAC;MACDoB,MAAM,EAAEd,GAAG,CAACI,IAAI,KAAK,QAAQ,IAAIJ,GAAG,CAACI,IAAI,KAAK,MAAM,IAAIJ,GAAG,CAACI,IAAI,KAAK,UAAU;MAC/EW,KAAK,EAAEf,GAAG,CAACI,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG;IACrC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMY,aAAa,GAAG9E,UAAU,CAACR,gBAAgB,CAAC;EAElD,oBACEX,OAAA;IAAKkG,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAxD,QAAA,eAC9B3C,OAAA,CAACd,MAAM;MAACgH,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAxD,QAAA,gBAChC3C,OAAA,CAACG,KAAK;QAAC6F,KAAK,EAAE,GAAI;QAACI,KAAK,EAAC,OAAO;QAACF,KAAK,EAAE;UAAEG,WAAW,EAAE;QAAoB,CAAE;QAAA1D,QAAA,gBAC3E3C,OAAA;UAAKkG,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAoB,CAAE;UAAA5D,QAAA,eACjE3C,OAAA;YAAA2C,QAAA,EAAI;UAAI;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN9F,OAAA,CAACb,IAAI;UACHqH,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE,CAAC,GAAGhG,gBAAgB,IAAIE,gBAAgB,EAAE,CAAE;UAC1D+F,KAAK,EAAElE,SAAU;UACjBmE,OAAO,EAAE1C,eAAgB;UACzBiC,KAAK,EAAE;YAAEC,MAAM,EAAE,mBAAmB;YAAEE,WAAW,EAAE;UAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAER9F,OAAA,CAACd,MAAM;QAAAyD,QAAA,eACL3C,OAAA,CAACI,OAAO;UAAC8F,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE;UAAO,CAAE;UAAAjE,QAAA,GACrDsD,aAAa,iBACZjG,OAAA,CAAAE,SAAA;YAAAyC,QAAA,gBACE3C,OAAA,CAACX,IAAI;cACH6F,KAAK,EAAEe,aAAa,CAACjE,IAAK;cAC1B6E,KAAK,eACH7G,OAAA,CAACJ,KAAK;gBAAA+C,QAAA,gBACJ3C,OAAA,CAACV,MAAM;kBACLqF,KAAK,EAAE9D,eAAgB;kBACvBiG,QAAQ,EAAEhG,kBAAmB;kBAC7BoF,KAAK,EAAE;oBAAEF,KAAK,EAAE;kBAAI,CAAE;kBACtBe,WAAW,EAAC,0BAAM;kBAAApE,QAAA,EAEjB5B,QAAQ,CAACiE,GAAG,CAACgC,OAAO,iBACnBhH,OAAA,CAACM,MAAM;oBAAkBqE,KAAK,EAAEqC,OAAO,CAACzD,EAAG;oBAAAZ,QAAA,eACzC3C,OAAA,CAACJ,KAAK;sBAAA+C,QAAA,GACHqE,OAAO,CAAChF,IAAI,eACbhC,OAAA,CAACL,GAAG;wBAACsH,KAAK,EAAED,OAAO,CAACE,YAAY,GAAG,OAAO,GAAG,KAAM;wBAAAvE,QAAA,EAChDqE,OAAO,CAACE,YAAY,GAAG,KAAK,GAAG;sBAAK;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GANGkB,OAAO,CAACzD,EAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOf,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACT9F,OAAA,CAACK,MAAM;kBACL0G,WAAW,EAAC,iBAAO;kBACnBI,UAAU;kBACVC,QAAQ,EAAE1C,YAAa;kBACvBwB,KAAK,EAAE;oBAAEF,KAAK,EAAE;kBAAI;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACF9F,OAAA,CAACR,MAAM;kBAAC6H,IAAI,eAAErH,OAAA,CAACH,cAAc;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAACa,OAAO,EAAE/B,aAAc;kBAAAjC,QAAA,EAAC;gBAE1D;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACR;cACDI,KAAK,EAAE;gBAAEoB,YAAY,EAAE;cAAG,CAAE;cAAA3E,QAAA,eAE5B3C,OAAA;gBAAGkG,KAAK,EAAE;kBAAEqB,MAAM,EAAE,CAAC;kBAAEN,KAAK,EAAE;gBAAO,CAAE;gBAAAtE,QAAA,EAAEsD,aAAa,CAACuB;cAAW;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEP9F,OAAA,CAACZ,KAAK;cACJ2F,OAAO,EAAEF,eAAe,CAAC,CAAE;cAC3B4C,UAAU,EAAExG,QAAS;cACrByG,MAAM,EAAC,IAAI;cACXrG,OAAO,EAAEA,OAAQ;cACjBE,UAAU,EAAE;gBACV,GAAGA,UAAU;gBACboG,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAEA,CAAClG,KAAK,EAAEmG,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQnG,KAAK;cAC1C,CAAE;cACFmF,QAAQ,EAAEtC,iBAAkB;cAC5BuD,MAAM,EAAE;gBAAEC,CAAC,EAAE;cAAc,CAAE;cAC7BC,IAAI,EAAC;YAAO;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,eACF,CACH,EAEA,CAACG,aAAa,iBACbjG,OAAA;YAAKkG,KAAK,EAAE;cAAEgC,SAAS,EAAE,QAAQ;cAAE5B,OAAO,EAAE;YAAO,CAAE;YAAA3D,QAAA,gBACnD3C,OAAA,CAACN,IAAI;cAACuI,IAAI,EAAC;YAAO;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrB9F,OAAA;cAAGkG,KAAK,EAAE;gBAAEiC,SAAS,EAAE;cAAG,CAAE;cAAAxF,QAAA,EAAC;YAAM;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACtF,EAAA,CA3RID,WAAqB;AAAA6H,EAAA,GAArB7H,WAAqB;AA6R3B,eAAeA,WAAW;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}