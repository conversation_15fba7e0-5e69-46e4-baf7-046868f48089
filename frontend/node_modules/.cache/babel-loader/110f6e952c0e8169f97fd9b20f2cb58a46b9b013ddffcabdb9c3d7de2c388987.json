{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Space } from 'antd';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { UserOutlined, SettingOutlined, LogoutOutlined, AppstoreOutlined, DatabaseOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst Layout = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const menuItems = [\n  // {\n  //   key: '/',\n  //   icon: <DashboardOutlined />,\n  //   label: '数据概览',\n  // },\n  {\n    key: '/data-details',\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    label: '数据明细'\n  }, {\n    key: '/data-update',\n    icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    label: '数据更新'\n  }, {\n    key: '/accounts',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    label: '社媒账号管理'\n  }, {\n    key: '/feishu-apps',\n    icon: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    label: '飞书应用管理'\n  }];\n  const userMenuItems = [{\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: logout\n  }];\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      width: 200,\n      theme: \"light\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          textAlign: 'center',\n          borderBottom: '1px solid #f0f0f0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u6570\\u636E\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: ({\n          key\n        }) => {\n          if (key === '/data-details') {\n            // 点击数据明细时，默认跳转到微信公众号总览\n            navigate('/data-details?platform=wechat_mp&type=overview');\n          } else {\n            navigate(key);\n          }\n        },\n        style: {\n          height: '100%',\n          borderRight: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          background: '#fff',\n          padding: '0 24px',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items: userMenuItems\n          },\n          placement: \"bottomRight\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: user === null || user === void 0 ? void 0 : user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '24px',\n          background: '#fff',\n          padding: '24px',\n          minHeight: 280\n        },\n        children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"5H37BwHXpxPtdZBjRNkyB+P8QZs=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "Space", "Outlet", "useNavigate", "useLocation", "UserOutlined", "SettingOutlined", "LogoutOutlined", "AppstoreOutlined", "DatabaseOutlined", "ReloadOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "_s", "navigate", "location", "user", "logout", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "userMenuItems", "onClick", "style", "minHeight", "children", "width", "theme", "padding", "textAlign", "borderBottom", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "height", "borderRight", "background", "display", "justifyContent", "alignItems", "menu", "placement", "cursor", "username", "margin", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Space } from 'antd';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { UserOutlined, DashboardOutlined, SettingOutlined, LogoutOutlined, AppstoreOutlined, DatabaseOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst { Header, Sider, Content } = AntLayout;\n\nconst Layout: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout } = useAuth();\n\n  const menuItems = [\n    // {\n    //   key: '/',\n    //   icon: <DashboardOutlined />,\n    //   label: '数据概览',\n    // },\n    {\n      key: '/data-details',\n      icon: <DatabaseOutlined />,\n      label: '数据明细',\n    },\n    {\n      key: '/data-update',\n      icon: <ReloadOutlined />,\n      label: '数据更新',\n    },\n    {\n      key: '/accounts',\n      icon: <SettingOutlined />,\n      label: '社媒账号管理',\n    },\n    {\n      key: '/feishu-apps',\n      icon: <AppstoreOutlined />,\n      label: '飞书应用管理',\n    },\n  ];\n\n  const userMenuItems = [\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: logout,\n    },\n  ];\n\n  return (\n    <AntLayout style={{ minHeight: '100vh' }}>\n      <Sider width={200} theme=\"light\">\n        <div style={{ padding: '16px', textAlign: 'center', borderBottom: '1px solid #f0f0f0' }}>\n          <h3>数据管理系统</h3>\n        </div>\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={({ key }) => {\n            if (key === '/data-details') {\n              // 点击数据明细时，默认跳转到微信公众号总览\n              navigate('/data-details?platform=wechat_mp&type=overview');\n            } else {\n              navigate(key);\n            }\n          }}\n          style={{ height: '100%', borderRight: 0 }}\n        />\n      </Sider>\n      \n      <AntLayout>\n        <Header style={{ background: '#fff', padding: '0 24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div />\n          <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n            <Space style={{ cursor: 'pointer' }}>\n              <Avatar icon={<UserOutlined />} />\n              <span>{user?.username}</span>\n            </Space>\n          </Dropdown>\n        </Header>\n        \n        <Content style={{ margin: '24px', background: '#fff', padding: '24px', minHeight: 280 }}>\n          <Outlet />\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AACzE,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SAASC,YAAY,EAAqBC,eAAe,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,mBAAmB;AACxJ,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGnB,SAAS;AAE5C,MAAMD,MAAgB,GAAGA,CAAA,KAAM;EAAAqB,EAAA;EAC7B,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAElC,MAAMW,SAAS,GAAG;EAChB;EACA;EACA;EACA;EACA;EACA;IACEC,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAEX,OAAA,CAACJ,gBAAgB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,cAAc;IACnBC,IAAI,eAAEX,OAAA,CAACH,cAAc;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEX,OAAA,CAACP,eAAe;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,cAAc;IACnBC,IAAI,eAAEX,OAAA,CAACL,gBAAgB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,aAAa,GAAG,CACpB;IACEP,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEX,OAAA,CAACN,cAAc;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbE,OAAO,EAAEV;EACX,CAAC,CACF;EAED,oBACER,OAAA,CAAChB,SAAS;IAACmC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBACvCrB,OAAA,CAACE,KAAK;MAACoB,KAAK,EAAE,GAAI;MAACC,KAAK,EAAC,OAAO;MAAAF,QAAA,gBAC9BrB,OAAA;QAAKmB,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAoB,CAAE;QAAAL,QAAA,eACtFrB,OAAA;UAAAqB,QAAA,EAAI;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACNf,OAAA,CAACf,IAAI;QACH0C,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAACtB,QAAQ,CAACuB,QAAQ,CAAE;QAClCC,KAAK,EAAErB,SAAU;QACjBS,OAAO,EAAEA,CAAC;UAAER;QAAI,CAAC,KAAK;UACpB,IAAIA,GAAG,KAAK,eAAe,EAAE;YAC3B;YACAL,QAAQ,CAAC,gDAAgD,CAAC;UAC5D,CAAC,MAAM;YACLA,QAAQ,CAACK,GAAG,CAAC;UACf;QACF,CAAE;QACFS,KAAK,EAAE;UAAEY,MAAM,EAAE,MAAM;UAAEC,WAAW,EAAE;QAAE;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERf,OAAA,CAAChB,SAAS;MAAAqC,QAAA,gBACRrB,OAAA,CAACC,MAAM;QAACkB,KAAK,EAAE;UAAEc,UAAU,EAAE,MAAM;UAAET,OAAO,EAAE,QAAQ;UAAEU,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAf,QAAA,gBAC/HrB,OAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPf,OAAA,CAACb,QAAQ;UAACkD,IAAI,EAAE;YAAEP,KAAK,EAAEb;UAAc,CAAE;UAACqB,SAAS,EAAC,aAAa;UAAAjB,QAAA,eAC/DrB,OAAA,CAACZ,KAAK;YAAC+B,KAAK,EAAE;cAAEoB,MAAM,EAAE;YAAU,CAAE;YAAAlB,QAAA,gBAClCrB,OAAA,CAACd,MAAM;cAACyB,IAAI,eAAEX,OAAA,CAACR,YAAY;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCf,OAAA;cAAAqB,QAAA,EAAOd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC;YAAQ;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAETf,OAAA,CAACG,OAAO;QAACgB,KAAK,EAAE;UAAEsB,MAAM,EAAE,MAAM;UAAER,UAAU,EAAE,MAAM;UAAET,OAAO,EAAE,MAAM;UAAEJ,SAAS,EAAE;QAAI,CAAE;QAAAC,QAAA,eACtFrB,OAAA,CAACX,MAAM;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACX,EAAA,CAjFIrB,MAAgB;EAAA,QACHO,WAAW,EACXC,WAAW,EACHO,OAAO;AAAA;AAAA4C,EAAA,GAH5B3D,MAAgB;AAmFtB,eAAeA,MAAM;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}