{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport { useSearchParams } from 'react-router-dom';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider,\n  Content\n} = Layout;\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst DataDetails = () => {\n  _s();\n  var _accountSummary$date_, _growthSummary$user_s;\n  const [searchParams] = useSearchParams();\n\n  // 从URL参数获取初始值\n  const initialPlatform = searchParams.get('platform') || 'wechat_mp';\n  const initialDataType = searchParams.get('type') || 'content_trend';\n  const [selectedPlatform, setSelectedPlatform] = useState(initialPlatform);\n  const [selectedDataType, setSelectedDataType] = useState(initialDataType);\n  const [selectedAccount, setSelectedAccount] = useState('all');\n  const [accounts, setAccounts] = useState([]);\n  const [dataList, setDataList] = useState([]);\n  const [dataConfig, setDataConfig] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [accountSummary, setAccountSummary] = useState(null);\n  const [growthSummary, setGrowthSummary] = useState(null);\n  const [overviewLoading, setOverviewLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 20,\n    total: 0\n  });\n  const [searchText, setSearchText] = useState('');\n\n  // 平台配置\n  const platformConfig = {\n    wechat_mp: {\n      name: '微信公众号',\n      dataTypes: {\n        content_trend: '内容数据趋势明细',\n        content_source: '内容流量来源明细',\n        content_detail: '内容已通知内容明细',\n        user_channel: '用户增长明细',\n        user_source: '用户来源明细'\n      }\n    },\n    wechat_channels: {\n      name: '视频号',\n      dataTypes: {}\n    },\n    xiaohongshu: {\n      name: '小红书',\n      dataTypes: {}\n    }\n  };\n\n  // 菜单项\n  const menuItems = [{\n    key: 'wechat_mp',\n    label: '微信公众号',\n    children: [{\n      key: 'wechat_mp_overview',\n      label: '总览'\n    }, {\n      key: 'wechat_mp_content_trend',\n      label: '内容数据趋势明细'\n    }, {\n      key: 'wechat_mp_content_source',\n      label: '内容流量来源明细'\n    }, {\n      key: 'wechat_mp_content_detail',\n      label: '内容已通知内容明细'\n    }, {\n      key: 'wechat_mp_user_channel',\n      label: '用户增长明细'\n    }, {\n      key: 'wechat_mp_user_source',\n      label: '用户来源明细'\n    }]\n  }, {\n    key: 'wechat_channels',\n    label: '视频号',\n    disabled: true,\n    children: [{\n      key: 'wechat_channels_placeholder',\n      label: '敬请期待',\n      disabled: true\n    }]\n  }, {\n    key: 'xiaohongshu',\n    label: '小红书',\n    disabled: true,\n    children: [{\n      key: 'xiaohongshu_placeholder',\n      label: '敬请期待',\n      disabled: true\n    }]\n  }];\n  useEffect(() => {\n    fetchDataConfig();\n    fetchAccounts();\n  }, []);\n\n  // 处理URL参数变化\n  useEffect(() => {\n    const platform = searchParams.get('platform');\n    const type = searchParams.get('type');\n    if (platform) {\n      setSelectedPlatform(platform);\n    }\n    if (type) {\n      setSelectedDataType(type);\n    }\n  }, [searchParams]);\n  useEffect(() => {\n    if (selectedAccount && selectedDataType) {\n      if (selectedDataType === 'overview') {\n        fetchOverviewData();\n      } else {\n        fetchDataList();\n      }\n    }\n  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);\n  const fetchDataConfig = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/config');\n      if (response.data.success) {\n        setDataConfig(response.data.data_types);\n      }\n    } catch (error) {\n      console.error('获取数据配置失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取数据配置失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取数据配置失败');\n      }\n    }\n  };\n  const fetchAccounts = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/accounts');\n      if (response.data.success) {\n        setAccounts(response.data.accounts);\n        // 默认选择\"全部\"\n        setSelectedAccount('all');\n      }\n    } catch (error) {\n      console.error('获取账号列表失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取账号列表失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取账号列表失败');\n      }\n    }\n  };\n  const fetchDataList = async () => {\n    if (!selectedAccount || !selectedDataType) return;\n    setLoading(true);\n    try {\n      const params = {\n        page: pagination.current,\n        page_size: pagination.pageSize,\n        search: searchText || undefined,\n        sort_field: 'created_at',\n        sort_order: 'desc'\n      };\n\n      // 只有当选择了具体账号时才传递 account_id 参数\n      if (selectedAccount !== 'all') {\n        params.account_id = selectedAccount;\n      }\n      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, {\n        params\n      });\n      if (response.data.success) {\n        setDataList(response.data.data);\n        setPagination(prev => ({\n          ...prev,\n          total: response.data.total,\n          current: response.data.page\n        }));\n      } else {\n        message.error(response.data.error || '获取数据失败');\n      }\n    } catch (error) {\n      message.error('获取数据列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchOverviewData = async () => {\n    setOverviewLoading(true);\n    try {\n      // 并行获取两个总览数据\n      const [accountSummaryResponse, growthSummaryResponse] = await Promise.all([api.get('/data-details/wechat-mp/overview/account-summary'), api.get('/data-details/wechat-mp/overview/growth-summary')]);\n      if (accountSummaryResponse.data.success) {\n        setAccountSummary(accountSummaryResponse.data);\n      } else {\n        message.error('获取账号汇总数据失败');\n      }\n      if (growthSummaryResponse.data.success) {\n        setGrowthSummary(growthSummaryResponse.data);\n      } else {\n        message.error('获取增长汇总数据失败');\n      }\n    } catch (error) {\n      console.error('获取总览数据失败:', error);\n      message.error('获取总览数据失败');\n    } finally {\n      setOverviewLoading(false);\n    }\n  };\n  const handleMenuClick = ({\n    key\n  }) => {\n    const parts = key.split('_');\n    if (parts.length >= 3) {\n      const platform = parts.slice(0, 2).join('_'); // wechat_mp\n      const dataType = parts.slice(2).join('_'); // content_trend\n\n      setSelectedPlatform(platform);\n      setSelectedDataType(dataType);\n      setPagination(prev => ({\n        ...prev,\n        current: 1\n      }));\n    }\n  };\n  const handleTableChange = paginationInfo => {\n    setPagination(prev => ({\n      ...prev,\n      current: paginationInfo.current,\n      pageSize: paginationInfo.pageSize\n    }));\n  };\n  const handleSearch = value => {\n    setSearchText(value);\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleRefresh = () => {\n    fetchDataList();\n  };\n\n  // 用户来源映射\n  const userSourceMapping = {\n    \"0\": \"其他合计\",\n    \"1\": \"公众号搜索\",\n    \"17\": \"名片分享\",\n    \"30\": \"扫描二维码\",\n    \"57\": \"文章内账号名称\",\n    \"100\": \"微信广告\",\n    \"161\": \"他人转载\",\n    \"149\": \"小程序关注\",\n    \"200\": \"视频号\",\n    \"201\": \"直播\"\n  };\n\n  // 生成表格列配置\n  const generateColumns = () => {\n    const config = dataConfig[selectedDataType];\n    if (!config) return [];\n    return config.columns.map(col => ({\n      title: col.title,\n      dataIndex: col.key,\n      key: col.key,\n      render: value => {\n        if (col.type === 'date') {\n          return value ? new Date(value).toLocaleDateString() : '-';\n        }\n        if (col.type === 'datetime') {\n          return value ? new Date(value).toLocaleString() : '-';\n        }\n        if (col.type === 'number') {\n          return typeof value === 'number' ? value.toLocaleString() : value || 0;\n        }\n        if (col.type === 'url') {\n          return value ? /*#__PURE__*/_jsxDEV(\"a\", {\n            href: value,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"\\u67E5\\u770B\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this) : '-';\n        }\n        if (col.type === 'user_source') {\n          return userSourceMapping[String(value)] || `未知来源(${value})`;\n        }\n        return value || '-';\n      },\n      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',\n      width: col.key === 'account_name' ? 150 : col.key === 'updated_at' ? 180 : col.type === 'text' ? 200 : 120,\n      fixed: col.key === 'account_name' ? 'left' : col.key === 'updated_at' ? 'right' : undefined\n    }));\n  };\n  const currentConfig = dataConfig[selectedDataType];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh'\n    },\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        height: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        width: 250,\n        theme: \"light\",\n        style: {\n          borderRight: '1px solid #f0f0f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #f0f0f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u6570\\u636E\\u660E\\u7EC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          mode: \"inline\",\n          selectedKeys: [`${selectedPlatform}_${selectedDataType}`],\n          items: menuItems,\n          onClick: handleMenuClick,\n          style: {\n            height: 'calc(100% - 64px)',\n            borderRight: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Content, {\n          style: {\n            padding: '24px',\n            background: '#fff'\n          },\n          children: selectedDataType === 'overview' ?\n          /*#__PURE__*/\n          // 总览视图\n          _jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                marginBottom: 24\n              },\n              children: \"\\u6570\\u636E\\u603B\\u89C8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u793E\\u5A92\\u8D26\\u53F7\\u6570\\u636E\\u6C47\\u603B\\uFF08\\u6570\\u636E\\u65E5\\u671F\\u8DE8\\u5EA6\\uFF09\",\n              style: {\n                marginBottom: 24\n              },\n              loading: overviewLoading,\n              children: accountSummary && /*#__PURE__*/_jsxDEV(Table, {\n                dataSource: accountSummary.data,\n                rowKey: \"account_name\",\n                pagination: false,\n                scroll: {\n                  x: 'max-content'\n                },\n                size: \"small\",\n                columns: [{\n                  title: '账号名称',\n                  dataIndex: 'account_name',\n                  key: 'account_name',\n                  fixed: 'left',\n                  width: 150\n                }, {\n                  title: '公众号关注数',\n                  children: ((_accountSummary$date_ = accountSummary.date_columns) === null || _accountSummary$date_ === void 0 ? void 0 : _accountSummary$date_.map(dateCol => ({\n                    title: dateCol,\n                    dataIndex: dateCol,\n                    key: dateCol,\n                    width: 120,\n                    render: value => (value === null || value === void 0 ? void 0 : value.toLocaleString()) || '-'\n                  }))) || []\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u5173\\u6CE8\\u6570\\u5408\\u8BA1\\u51C0\\u589E\\u957F\\uFF08\\u6570\\u636E\\u65E5\\u671F\\u8DE8\\u5EA6\\uFF09\",\n              loading: overviewLoading,\n              children: growthSummary && /*#__PURE__*/_jsxDEV(Table, {\n                dataSource: growthSummary.data,\n                rowKey: \"account_name\",\n                pagination: false,\n                scroll: {\n                  x: 'max-content'\n                },\n                size: \"small\",\n                columns: [{\n                  title: '账号名称',\n                  dataIndex: 'account_name',\n                  key: 'account_name',\n                  fixed: 'left',\n                  width: 150\n                }, {\n                  title: '新增粉丝',\n                  dataIndex: 'new_user',\n                  key: 'new_user',\n                  width: 100,\n                  render: value => (value === null || value === void 0 ? void 0 : value.toLocaleString()) || 0\n                }, {\n                  title: '取消关注',\n                  dataIndex: 'cancel_user',\n                  key: 'cancel_user',\n                  width: 100,\n                  render: value => (value === null || value === void 0 ? void 0 : value.toLocaleString()) || 0\n                }, {\n                  title: '累计关注粉丝',\n                  dataIndex: 'cumulate_user',\n                  key: 'cumulate_user',\n                  width: 120,\n                  render: value => (value === null || value === void 0 ? void 0 : value.toLocaleString()) || 0\n                }, ...(((_growthSummary$user_s = growthSummary.user_sources) === null || _growthSummary$user_s === void 0 ? void 0 : _growthSummary$user_s.map(source => ({\n                  title: userSourceMapping[String(source)] || `来源${source}`,\n                  dataIndex: `source_${source}`,\n                  key: `source_${source}`,\n                  width: 100,\n                  render: value => (value === null || value === void 0 ? void 0 : value.toLocaleString()) || 0\n                }))) || [])]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this) : currentConfig ?\n          /*#__PURE__*/\n          // 原有的数据明细视图\n          _jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: currentConfig.name,\n              extra: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: selectedAccount,\n                  onChange: setSelectedAccount,\n                  style: {\n                    width: 200\n                  },\n                  placeholder: \"\\u9009\\u62E9\\u8D26\\u53F7\",\n                  children: [/*#__PURE__*/_jsxDEV(Option, {\n                    value: \"all\",\n                    children: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [\"\\u5168\\u90E8\\u8D26\\u53F7\", /*#__PURE__*/_jsxDEV(Tag, {\n                        color: \"blue\",\n                        children: [accounts.length, \" \\u4E2A\\u8D26\\u53F7\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 459,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 27\n                    }, this)\n                  }, \"all\", false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 25\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(Option, {\n                    value: account.id,\n                    children: /*#__PURE__*/_jsxDEV(Space, {\n                      children: account.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 29\n                    }, this)\n                  }, account.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Search, {\n                  placeholder: \"\\u641C\\u7D22...\",\n                  allowClear: true,\n                  onSearch: handleSearch,\n                  style: {\n                    width: 200\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 37\n                  }, this),\n                  onClick: handleRefresh,\n                  children: \"\\u5237\\u65B0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 21\n              }, this),\n              style: {\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  color: '#666'\n                },\n                children: currentConfig.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table, {\n              columns: generateColumns(),\n              dataSource: dataList,\n              rowKey: \"id\",\n              loading: loading,\n              pagination: {\n                ...pagination,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`\n              },\n              onChange: handleTableChange,\n              scroll: {\n                x: 'max-content'\n              },\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Spin, {\n              size: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginTop: 16\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 332,\n    columnNumber: 5\n  }, this);\n};\n_s(DataDetails, \"zQwH/QWn5QvWYV2/JMUiIx2bN6o=\", false, function () {\n  return [useSearchParams];\n});\n_c = DataDetails;\nexport default DataDetails;\nvar _c;\n$RefreshReg$(_c, \"DataDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "<PERSON><PERSON>", "Table", "Card", "Select", "Input", "<PERSON><PERSON>", "message", "Spin", "Tag", "Space", "ReloadOutlined", "useSearchParams", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Content", "Search", "Option", "DataDetails", "_s", "_accountSummary$date_", "_growthSummary$user_s", "searchParams", "initialPlatform", "get", "initialDataType", "selectedPlatform", "setSelectedPlatform", "selectedDataType", "setSelectedDataType", "selectedAccount", "setSelectedAccount", "accounts", "setAccounts", "dataList", "setDataList", "dataConfig", "setDataConfig", "loading", "setLoading", "accountSummary", "setAccountSummary", "growthSummary", "setGrowthSummary", "overviewLoading", "setOverviewLoading", "pagination", "setPagination", "current", "pageSize", "total", "searchText", "setSearchText", "platformConfig", "wechat_mp", "name", "dataTypes", "content_trend", "content_source", "content_detail", "user_channel", "user_source", "wechat_channels", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menuItems", "key", "label", "children", "disabled", "fetchDataConfig", "fetchAccounts", "platform", "type", "fetchOverviewData", "fetchDataList", "response", "data", "success", "data_types", "error", "console", "detail", "params", "page", "page_size", "search", "undefined", "sort_field", "sort_order", "account_id", "prev", "accountSummaryResponse", "growthSummaryResponse", "Promise", "all", "handleMenuClick", "parts", "split", "length", "slice", "join", "dataType", "handleTableChange", "paginationInfo", "handleSearch", "value", "handleRefresh", "userSourceMapping", "generateColumns", "config", "columns", "map", "col", "title", "dataIndex", "render", "Date", "toLocaleDateString", "toLocaleString", "href", "target", "rel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "String", "sorter", "width", "fixed", "currentConfig", "style", "height", "theme", "borderRight", "padding", "borderBottom", "mode", "<PERSON><PERSON><PERSON><PERSON>", "items", "onClick", "background", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "size", "date_columns", "dateCol", "user_sources", "source", "extra", "onChange", "placeholder", "color", "account", "id", "allowClear", "onSearch", "icon", "margin", "description", "showSizeChanger", "showQuickJumper", "showTotal", "range", "textAlign", "marginTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';\nimport { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';\nimport { useSearchParams } from 'react-router-dom';\nimport api from '../services/api';\n\nconst { Sider, Content } = Layout;\nconst { Search } = Input;\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n}\n\ninterface DataItem {\n  id: number;\n  account_id: number;\n  [key: string]: any;\n}\n\ninterface DataConfig {\n  name: string;\n  description: string;\n  columns: Array<{\n    key: string;\n    title: string;\n    type: string;\n  }>;\n}\n\ninterface DataTypeConfig {\n  [key: string]: DataConfig;\n}\n\nconst DataDetails: React.FC = () => {\n  const [searchParams] = useSearchParams();\n\n  // 从URL参数获取初始值\n  const initialPlatform = searchParams.get('platform') || 'wechat_mp';\n  const initialDataType = searchParams.get('type') || 'content_trend';\n\n  const [selectedPlatform, setSelectedPlatform] = useState<string>(initialPlatform);\n  const [selectedDataType, setSelectedDataType] = useState<string>(initialDataType);\n  const [selectedAccount, setSelectedAccount] = useState<number | string | null>('all');\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [dataList, setDataList] = useState<DataItem[]>([]);\n  const [dataConfig, setDataConfig] = useState<DataTypeConfig>({});\n  const [loading, setLoading] = useState(false);\n  const [accountSummary, setAccountSummary] = useState<any>(null);\n  const [growthSummary, setGrowthSummary] = useState<any>(null);\n  const [overviewLoading, setOverviewLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 20,\n    total: 0,\n  });\n  const [searchText, setSearchText] = useState<string>('');\n\n  // 平台配置\n  const platformConfig = {\n    wechat_mp: {\n      name: '微信公众号',\n      dataTypes: {\n        content_trend: '内容数据趋势明细',\n        content_source: '内容流量来源明细',\n        content_detail: '内容已通知内容明细',\n        user_channel: '用户增长明细',\n        user_source: '用户来源明细'\n      }\n    },\n    wechat_channels: {\n      name: '视频号',\n      dataTypes: {}\n    },\n    xiaohongshu: {\n      name: '小红书',\n      dataTypes: {}\n    }\n  };\n\n  // 菜单项\n  const menuItems = [\n    {\n      key: 'wechat_mp',\n      label: '微信公众号',\n      children: [\n        { key: 'wechat_mp_overview', label: '总览' },\n        { key: 'wechat_mp_content_trend', label: '内容数据趋势明细' },\n        { key: 'wechat_mp_content_source', label: '内容流量来源明细' },\n        { key: 'wechat_mp_content_detail', label: '内容已通知内容明细' },\n        { key: 'wechat_mp_user_channel', label: '用户增长明细' },\n        { key: 'wechat_mp_user_source', label: '用户来源明细' }\n      ]\n    },\n    {\n      key: 'wechat_channels',\n      label: '视频号',\n      disabled: true,\n      children: [\n        { key: 'wechat_channels_placeholder', label: '敬请期待', disabled: true }\n      ]\n    },\n    {\n      key: 'xiaohongshu',\n      label: '小红书',\n      disabled: true,\n      children: [\n        { key: 'xiaohongshu_placeholder', label: '敬请期待', disabled: true }\n      ]\n    }\n  ];\n\n  useEffect(() => {\n    fetchDataConfig();\n    fetchAccounts();\n  }, []);\n\n  // 处理URL参数变化\n  useEffect(() => {\n    const platform = searchParams.get('platform');\n    const type = searchParams.get('type');\n\n    if (platform) {\n      setSelectedPlatform(platform);\n    }\n    if (type) {\n      setSelectedDataType(type);\n    }\n  }, [searchParams]);\n\n  useEffect(() => {\n    if (selectedAccount && selectedDataType) {\n      if (selectedDataType === 'overview') {\n        fetchOverviewData();\n      } else {\n        fetchDataList();\n      }\n    }\n  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);\n\n  const fetchDataConfig = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/config');\n      if (response.data.success) {\n        setDataConfig(response.data.data_types);\n      }\n    } catch (error: any) {\n      console.error('获取数据配置失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取数据配置失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取数据配置失败');\n      }\n    }\n  };\n\n  const fetchAccounts = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/accounts');\n      if (response.data.success) {\n        setAccounts(response.data.accounts);\n        // 默认选择\"全部\"\n        setSelectedAccount('all');\n      }\n    } catch (error: any) {\n      console.error('获取账号列表失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取账号列表失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取账号列表失败');\n      }\n    }\n  };\n\n  const fetchDataList = async () => {\n    if (!selectedAccount || !selectedDataType) return;\n\n    setLoading(true);\n    try {\n      const params: any = {\n        page: pagination.current,\n        page_size: pagination.pageSize,\n        search: searchText || undefined,\n        sort_field: 'created_at',\n        sort_order: 'desc'\n      };\n\n      // 只有当选择了具体账号时才传递 account_id 参数\n      if (selectedAccount !== 'all') {\n        params.account_id = selectedAccount;\n      }\n\n      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, { params });\n      \n      if (response.data.success) {\n        setDataList(response.data.data);\n        setPagination(prev => ({\n          ...prev,\n          total: response.data.total,\n          current: response.data.page\n        }));\n      } else {\n        message.error(response.data.error || '获取数据失败');\n      }\n    } catch (error) {\n      message.error('获取数据列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchOverviewData = async () => {\n    setOverviewLoading(true);\n    try {\n      // 并行获取两个总览数据\n      const [accountSummaryResponse, growthSummaryResponse] = await Promise.all([\n        api.get('/data-details/wechat-mp/overview/account-summary'),\n        api.get('/data-details/wechat-mp/overview/growth-summary')\n      ]);\n\n      if (accountSummaryResponse.data.success) {\n        setAccountSummary(accountSummaryResponse.data);\n      } else {\n        message.error('获取账号汇总数据失败');\n      }\n\n      if (growthSummaryResponse.data.success) {\n        setGrowthSummary(growthSummaryResponse.data);\n      } else {\n        message.error('获取增长汇总数据失败');\n      }\n    } catch (error: any) {\n      console.error('获取总览数据失败:', error);\n      message.error('获取总览数据失败');\n    } finally {\n      setOverviewLoading(false);\n    }\n  };\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    const parts = key.split('_');\n    if (parts.length >= 3) {\n      const platform = parts.slice(0, 2).join('_'); // wechat_mp\n      const dataType = parts.slice(2).join('_'); // content_trend\n      \n      setSelectedPlatform(platform);\n      setSelectedDataType(dataType);\n      setPagination(prev => ({ ...prev, current: 1 }));\n    }\n  };\n\n  const handleTableChange = (paginationInfo: any) => {\n    setPagination(prev => ({\n      ...prev,\n      current: paginationInfo.current,\n      pageSize: paginationInfo.pageSize\n    }));\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchText(value);\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handleRefresh = () => {\n    fetchDataList();\n  };\n\n  // 用户来源映射\n  const userSourceMapping: { [key: string]: string } = {\n    \"0\": \"其他合计\",\n    \"1\": \"公众号搜索\",\n    \"17\": \"名片分享\",\n    \"30\": \"扫描二维码\",\n    \"57\": \"文章内账号名称\",\n    \"100\": \"微信广告\",\n    \"161\": \"他人转载\",\n    \"149\": \"小程序关注\",\n    \"200\": \"视频号\",\n    \"201\": \"直播\"\n  };\n\n  // 生成表格列配置\n  const generateColumns = () => {\n    const config = dataConfig[selectedDataType];\n    if (!config) return [];\n\n    return config.columns.map(col => ({\n      title: col.title,\n      dataIndex: col.key,\n      key: col.key,\n      render: (value: any) => {\n        if (col.type === 'date') {\n          return value ? new Date(value).toLocaleDateString() : '-';\n        }\n        if (col.type === 'datetime') {\n          return value ? new Date(value).toLocaleString() : '-';\n        }\n        if (col.type === 'number') {\n          return typeof value === 'number' ? value.toLocaleString() : value || 0;\n        }\n        if (col.type === 'url') {\n          return value ? (\n            <a href={value} target=\"_blank\" rel=\"noopener noreferrer\">\n              查看链接\n            </a>\n          ) : '-';\n        }\n        if (col.type === 'user_source') {\n          return userSourceMapping[String(value)] || `未知来源(${value})`;\n        }\n        return value || '-';\n      },\n      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',\n      width: col.key === 'account_name' ? 150 :\n             col.key === 'updated_at' ? 180 :\n             col.type === 'text' ? 200 : 120,\n      fixed: col.key === 'account_name' ? 'left' as const :\n             col.key === 'updated_at' ? 'right' as const : undefined\n    }));\n  };\n\n  const currentConfig = dataConfig[selectedDataType];\n\n  return (\n    <div style={{ height: '100vh' }}>\n      <Layout style={{ height: '100%' }}>\n        <Sider width={250} theme=\"light\" style={{ borderRight: '1px solid #f0f0f0' }}>\n          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>\n            <h3>数据明细</h3>\n          </div>\n          <Menu\n            mode=\"inline\"\n            selectedKeys={[`${selectedPlatform}_${selectedDataType}`]}\n            items={menuItems}\n            onClick={handleMenuClick}\n            style={{ height: 'calc(100% - 64px)', borderRight: 0 }}\n          />\n        </Sider>\n        \n        <Layout>\n          <Content style={{ padding: '24px', background: '#fff' }}>\n            {selectedDataType === 'overview' ? (\n              // 总览视图\n              <div>\n                <h2 style={{ marginBottom: 24 }}>数据总览</h2>\n\n                {/* 社媒账号数据汇总表 */}\n                <Card\n                  title=\"社媒账号数据汇总（数据日期跨度）\"\n                  style={{ marginBottom: 24 }}\n                  loading={overviewLoading}\n                >\n                  {accountSummary && (\n                    <Table\n                      dataSource={accountSummary.data}\n                      rowKey=\"account_name\"\n                      pagination={false}\n                      scroll={{ x: 'max-content' }}\n                      size=\"small\"\n                      columns={[\n                        {\n                          title: '账号名称',\n                          dataIndex: 'account_name',\n                          key: 'account_name',\n                          fixed: 'left',\n                          width: 150\n                        },\n                        {\n                          title: '公众号关注数',\n                          children: accountSummary.date_columns?.map((dateCol: string) => ({\n                            title: dateCol,\n                            dataIndex: dateCol,\n                            key: dateCol,\n                            width: 120,\n                            render: (value: number) => value?.toLocaleString() || '-'\n                          })) || []\n                        }\n                      ]}\n                    />\n                  )}\n                </Card>\n\n                {/* 关注数合计净增长表 */}\n                <Card\n                  title=\"关注数合计净增长（数据日期跨度）\"\n                  loading={overviewLoading}\n                >\n                  {growthSummary && (\n                    <Table\n                      dataSource={growthSummary.data}\n                      rowKey=\"account_name\"\n                      pagination={false}\n                      scroll={{ x: 'max-content' }}\n                      size=\"small\"\n                      columns={[\n                        {\n                          title: '账号名称',\n                          dataIndex: 'account_name',\n                          key: 'account_name',\n                          fixed: 'left',\n                          width: 150\n                        },\n                        {\n                          title: '新增粉丝',\n                          dataIndex: 'new_user',\n                          key: 'new_user',\n                          width: 100,\n                          render: (value: number) => value?.toLocaleString() || 0\n                        },\n                        {\n                          title: '取消关注',\n                          dataIndex: 'cancel_user',\n                          key: 'cancel_user',\n                          width: 100,\n                          render: (value: number) => value?.toLocaleString() || 0\n                        },\n                        {\n                          title: '累计关注粉丝',\n                          dataIndex: 'cumulate_user',\n                          key: 'cumulate_user',\n                          width: 120,\n                          render: (value: number) => value?.toLocaleString() || 0\n                        },\n                        ...(growthSummary.user_sources?.map((source: number) => ({\n                          title: userSourceMapping[String(source)] || `来源${source}`,\n                          dataIndex: `source_${source}`,\n                          key: `source_${source}`,\n                          width: 100,\n                          render: (value: number) => value?.toLocaleString() || 0\n                        })) || [])\n                      ]}\n                    />\n                  )}\n                </Card>\n              </div>\n            ) : currentConfig ? (\n              // 原有的数据明细视图\n              <>\n                <Card\n                  title={currentConfig.name}\n                  extra={\n                    <Space>\n                      <Select\n                        value={selectedAccount}\n                        onChange={setSelectedAccount}\n                        style={{ width: 200 }}\n                        placeholder=\"选择账号\"\n                      >\n                        <Option key=\"all\" value=\"all\">\n                          <Space>\n                            全部账号\n                            <Tag color=\"blue\">\n                              {accounts.length} 个账号\n                            </Tag>\n                          </Space>\n                        </Option>\n                        {accounts.map(account => (\n                          <Option key={account.id} value={account.id}>\n                            <Space>\n                              {account.name}\n                            </Space>\n                          </Option>\n                        ))}\n                      </Select>\n                      <Search\n                        placeholder=\"搜索...\"\n                        allowClear\n                        onSearch={handleSearch}\n                        style={{ width: 200 }}\n                      />\n                      <Button icon={<ReloadOutlined />} onClick={handleRefresh}>\n                        刷新\n                      </Button>\n                    </Space>\n                  }\n                  style={{ marginBottom: 16 }}\n                >\n                  <p style={{ margin: 0, color: '#666' }}>{currentConfig.description}</p>\n                </Card>\n\n                <Table\n                  columns={generateColumns()}\n                  dataSource={dataList}\n                  rowKey=\"id\"\n                  loading={loading}\n                  pagination={{\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range) =>\n                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`\n                  }}\n                  onChange={handleTableChange}\n                  scroll={{ x: 'max-content' }}\n                  size=\"small\"\n                />\n              </>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin size=\"large\" />\n                <p style={{ marginTop: 16 }}>加载中...</p>\n              </div>\n            )}\n          </Content>\n        </Layout>\n      </Layout>\n    </div>\n  );\n};\n\nexport default DataDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AAClG,SAAyBC,cAAc,QAA0B,mBAAmB;AACpF,SAASC,eAAe,QAAQ,kBAAkB;AAClD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGnB,MAAM;AACjC,MAAM;EAAEoB;AAAO,CAAC,GAAGf,KAAK;AACxB,MAAM;EAAEgB;AAAO,CAAC,GAAGjB,MAAM;AA8BzB,MAAMkB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EAClC,MAAM,CAACC,YAAY,CAAC,GAAGd,eAAe,CAAC,CAAC;;EAExC;EACA,MAAMe,eAAe,GAAGD,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC,IAAI,WAAW;EACnE,MAAMC,eAAe,GAAGH,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,eAAe;EAEnE,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAS6B,eAAe,CAAC;EACjF,MAAM,CAACK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAS+B,eAAe,CAAC;EACjF,MAAM,CAACK,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAyB,KAAK,CAAC;EACrF,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAa,EAAE,CAAC;EACxD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAiB,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAM,IAAI,CAAC;EAC/D,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC;IAC3CsD,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAS,EAAE,CAAC;;EAExD;EACA,MAAM2D,cAAc,GAAG;IACrBC,SAAS,EAAE;MACTC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE;QACTC,aAAa,EAAE,UAAU;QACzBC,cAAc,EAAE,UAAU;QAC1BC,cAAc,EAAE,WAAW;QAC3BC,YAAY,EAAE,QAAQ;QACtBC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,eAAe,EAAE;MACfP,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,CAAC;IACd,CAAC;IACDO,WAAW,EAAE;MACXR,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC1C;MAAED,GAAG,EAAE,yBAAyB;MAAEC,KAAK,EAAE;IAAW,CAAC,EACrD;MAAED,GAAG,EAAE,0BAA0B;MAAEC,KAAK,EAAE;IAAW,CAAC,EACtD;MAAED,GAAG,EAAE,0BAA0B;MAAEC,KAAK,EAAE;IAAY,CAAC,EACvD;MAAED,GAAG,EAAE,wBAAwB;MAAEC,KAAK,EAAE;IAAS,CAAC,EAClD;MAAED,GAAG,EAAE,uBAAuB;MAAEC,KAAK,EAAE;IAAS,CAAC;EAErD,CAAC,EACD;IACED,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,KAAK;IACZE,QAAQ,EAAE,IAAI;IACdD,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,6BAA6B;MAAEC,KAAK,EAAE,MAAM;MAAEE,QAAQ,EAAE;IAAK,CAAC;EAEzE,CAAC,EACD;IACEH,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,KAAK;IACZE,QAAQ,EAAE,IAAI;IACdD,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,yBAAyB;MAAEC,KAAK,EAAE,MAAM;MAAEE,QAAQ,EAAE;IAAK,CAAC;EAErE,CAAC,CACF;EAEDzE,SAAS,CAAC,MAAM;IACd0E,eAAe,CAAC,CAAC;IACjBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3E,SAAS,CAAC,MAAM;IACd,MAAM4E,QAAQ,GAAGjD,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC;IAC7C,MAAMgD,IAAI,GAAGlD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC;IAErC,IAAI+C,QAAQ,EAAE;MACZ5C,mBAAmB,CAAC4C,QAAQ,CAAC;IAC/B;IACA,IAAIC,IAAI,EAAE;MACR3C,mBAAmB,CAAC2C,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE,CAAClD,YAAY,CAAC,CAAC;EAElB3B,SAAS,CAAC,MAAM;IACd,IAAImC,eAAe,IAAIF,gBAAgB,EAAE;MACvC,IAAIA,gBAAgB,KAAK,UAAU,EAAE;QACnC6C,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM;QACLC,aAAa,CAAC,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAAC5C,eAAe,EAAEF,gBAAgB,EAAEkB,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,EAAEE,UAAU,CAAC,CAAC;EAE5F,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMlE,GAAG,CAACe,GAAG,CAAC,gCAAgC,CAAC;MAChE,IAAImD,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzBxC,aAAa,CAACsC,QAAQ,CAACC,IAAI,CAACE,UAAU,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIA,KAAK,CAACJ,QAAQ,EAAE;QAClBK,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACJ,QAAQ,CAACC,IAAI,CAAC;QAC3CzE,OAAO,CAAC4E,KAAK,CAAC,aAAaA,KAAK,CAACJ,QAAQ,CAACC,IAAI,CAACK,MAAM,IAAIF,KAAK,CAAC5E,OAAO,EAAE,CAAC;MAC3E,CAAC,MAAM;QACLA,OAAO,CAAC4E,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF;EACF,CAAC;EAED,MAAMT,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMlE,GAAG,CAACe,GAAG,CAAC,kCAAkC,CAAC;MAClE,IAAImD,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzB5C,WAAW,CAAC0C,QAAQ,CAACC,IAAI,CAAC5C,QAAQ,CAAC;QACnC;QACAD,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOgD,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIA,KAAK,CAACJ,QAAQ,EAAE;QAClBK,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACJ,QAAQ,CAACC,IAAI,CAAC;QAC3CzE,OAAO,CAAC4E,KAAK,CAAC,aAAaA,KAAK,CAACJ,QAAQ,CAACC,IAAI,CAACK,MAAM,IAAIF,KAAK,CAAC5E,OAAO,EAAE,CAAC;MAC3E,CAAC,MAAM;QACLA,OAAO,CAAC4E,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF;EACF,CAAC;EAED,MAAML,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC5C,eAAe,IAAI,CAACF,gBAAgB,EAAE;IAE3CW,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM2C,MAAW,GAAG;QAClBC,IAAI,EAAErC,UAAU,CAACE,OAAO;QACxBoC,SAAS,EAAEtC,UAAU,CAACG,QAAQ;QAC9BoC,MAAM,EAAElC,UAAU,IAAImC,SAAS;QAC/BC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE;MACd,CAAC;;MAED;MACA,IAAI1D,eAAe,KAAK,KAAK,EAAE;QAC7BoD,MAAM,CAACO,UAAU,GAAG3D,eAAe;MACrC;MAEA,MAAM6C,QAAQ,GAAG,MAAMlE,GAAG,CAACe,GAAG,CAAC,2BAA2BI,gBAAgB,EAAE,EAAE;QAAEsD;MAAO,CAAC,CAAC;MAEzF,IAAIP,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzB1C,WAAW,CAACwC,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC;QAC/B7B,aAAa,CAAC2C,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPxC,KAAK,EAAEyB,QAAQ,CAACC,IAAI,CAAC1B,KAAK;UAC1BF,OAAO,EAAE2B,QAAQ,CAACC,IAAI,CAACO;QACzB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLhF,OAAO,CAAC4E,KAAK,CAACJ,QAAQ,CAACC,IAAI,CAACG,KAAK,IAAI,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd5E,OAAO,CAAC4E,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC5B,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF;MACA,MAAM,CAAC8C,sBAAsB,EAAEC,qBAAqB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACxErF,GAAG,CAACe,GAAG,CAAC,kDAAkD,CAAC,EAC3Df,GAAG,CAACe,GAAG,CAAC,iDAAiD,CAAC,CAC3D,CAAC;MAEF,IAAImE,sBAAsB,CAACf,IAAI,CAACC,OAAO,EAAE;QACvCpC,iBAAiB,CAACkD,sBAAsB,CAACf,IAAI,CAAC;MAChD,CAAC,MAAM;QACLzE,OAAO,CAAC4E,KAAK,CAAC,YAAY,CAAC;MAC7B;MAEA,IAAIa,qBAAqB,CAAChB,IAAI,CAACC,OAAO,EAAE;QACtClC,gBAAgB,CAACiD,qBAAqB,CAAChB,IAAI,CAAC;MAC9C,CAAC,MAAM;QACLzE,OAAO,CAAC4E,KAAK,CAAC,YAAY,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5E,OAAO,CAAC4E,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRlC,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMkD,eAAe,GAAGA,CAAC;IAAE9B;EAAqB,CAAC,KAAK;IACpD,MAAM+B,KAAK,GAAG/B,GAAG,CAACgC,KAAK,CAAC,GAAG,CAAC;IAC5B,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;MACrB,MAAM3B,QAAQ,GAAGyB,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9C,MAAMC,QAAQ,GAAGL,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE3CzE,mBAAmB,CAAC4C,QAAQ,CAAC;MAC7B1C,mBAAmB,CAACwE,QAAQ,CAAC;MAC7BtD,aAAa,CAAC2C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE1C,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMsD,iBAAiB,GAAIC,cAAmB,IAAK;IACjDxD,aAAa,CAAC2C,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP1C,OAAO,EAAEuD,cAAc,CAACvD,OAAO;MAC/BC,QAAQ,EAAEsD,cAAc,CAACtD;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuD,YAAY,GAAIC,KAAa,IAAK;IACtCrD,aAAa,CAACqD,KAAK,CAAC;IACpB1D,aAAa,CAAC2C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1C,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAM0D,aAAa,GAAGA,CAAA,KAAM;IAC1BhC,aAAa,CAAC,CAAC;EACjB,CAAC;;EAED;EACA,MAAMiC,iBAA4C,GAAG;IACnD,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,OAAO;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,SAAS;IACf,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,MAAM,GAAGzE,UAAU,CAACR,gBAAgB,CAAC;IAC3C,IAAI,CAACiF,MAAM,EAAE,OAAO,EAAE;IAEtB,OAAOA,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,GAAG,KAAK;MAChCC,KAAK,EAAED,GAAG,CAACC,KAAK;MAChBC,SAAS,EAAEF,GAAG,CAAC/C,GAAG;MAClBA,GAAG,EAAE+C,GAAG,CAAC/C,GAAG;MACZkD,MAAM,EAAGV,KAAU,IAAK;QACtB,IAAIO,GAAG,CAACxC,IAAI,KAAK,MAAM,EAAE;UACvB,OAAOiC,KAAK,GAAG,IAAIW,IAAI,CAACX,KAAK,CAAC,CAACY,kBAAkB,CAAC,CAAC,GAAG,GAAG;QAC3D;QACA,IAAIL,GAAG,CAACxC,IAAI,KAAK,UAAU,EAAE;UAC3B,OAAOiC,KAAK,GAAG,IAAIW,IAAI,CAACX,KAAK,CAAC,CAACa,cAAc,CAAC,CAAC,GAAG,GAAG;QACvD;QACA,IAAIN,GAAG,CAACxC,IAAI,KAAK,QAAQ,EAAE;UACzB,OAAO,OAAOiC,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACa,cAAc,CAAC,CAAC,GAAGb,KAAK,IAAI,CAAC;QACxE;QACA,IAAIO,GAAG,CAACxC,IAAI,KAAK,KAAK,EAAE;UACtB,OAAOiC,KAAK,gBACV9F,OAAA;YAAG4G,IAAI,EAAEd,KAAM;YAACe,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAAtD,QAAA,EAAC;UAE1D;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GACF,GAAG;QACT;QACA,IAAIb,GAAG,CAACxC,IAAI,KAAK,aAAa,EAAE;UAC9B,OAAOmC,iBAAiB,CAACmB,MAAM,CAACrB,KAAK,CAAC,CAAC,IAAI,QAAQA,KAAK,GAAG;QAC7D;QACA,OAAOA,KAAK,IAAI,GAAG;MACrB,CAAC;MACDsB,MAAM,EAAEf,GAAG,CAACxC,IAAI,KAAK,QAAQ,IAAIwC,GAAG,CAACxC,IAAI,KAAK,MAAM,IAAIwC,GAAG,CAACxC,IAAI,KAAK,UAAU;MAC/EwD,KAAK,EAAEhB,GAAG,CAAC/C,GAAG,KAAK,cAAc,GAAG,GAAG,GAChC+C,GAAG,CAAC/C,GAAG,KAAK,YAAY,GAAG,GAAG,GAC9B+C,GAAG,CAACxC,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;MACtCyD,KAAK,EAAEjB,GAAG,CAAC/C,GAAG,KAAK,cAAc,GAAG,MAAM,GACnC+C,GAAG,CAAC/C,GAAG,KAAK,YAAY,GAAG,OAAO,GAAYqB;IACvD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4C,aAAa,GAAG9F,UAAU,CAACR,gBAAgB,CAAC;EAElD,oBACEjB,OAAA;IAAKwH,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAjE,QAAA,eAC9BxD,OAAA,CAACf,MAAM;MAACuI,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAjE,QAAA,gBAChCxD,OAAA,CAACG,KAAK;QAACkH,KAAK,EAAE,GAAI;QAACK,KAAK,EAAC,OAAO;QAACF,KAAK,EAAE;UAAEG,WAAW,EAAE;QAAoB,CAAE;QAAAnE,QAAA,gBAC3ExD,OAAA;UAAKwH,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAoB,CAAE;UAAArE,QAAA,eACjExD,OAAA;YAAAwD,QAAA,EAAI;UAAI;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNlH,OAAA,CAACd,IAAI;UACH4I,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE,CAAC,GAAGhH,gBAAgB,IAAIE,gBAAgB,EAAE,CAAE;UAC1D+G,KAAK,EAAE3E,SAAU;UACjB4E,OAAO,EAAE7C,eAAgB;UACzBoC,KAAK,EAAE;YAAEC,MAAM,EAAE,mBAAmB;YAAEE,WAAW,EAAE;UAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAERlH,OAAA,CAACf,MAAM;QAAAuE,QAAA,eACLxD,OAAA,CAACI,OAAO;UAACoH,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE;UAAO,CAAE;UAAA1E,QAAA,EACrDvC,gBAAgB,KAAK,UAAU;UAAA;UAC9B;UACAjB,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAIwH,KAAK,EAAE;gBAAEW,YAAY,EAAE;cAAG,CAAE;cAAA3E,QAAA,EAAC;YAAI;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAG1ClH,OAAA,CAACZ,IAAI;cACHkH,KAAK,EAAC,kGAAkB;cACxBkB,KAAK,EAAE;gBAAEW,YAAY,EAAE;cAAG,CAAE;cAC5BxG,OAAO,EAAEM,eAAgB;cAAAuB,QAAA,EAExB3B,cAAc,iBACb7B,OAAA,CAACb,KAAK;gBACJiJ,UAAU,EAAEvG,cAAc,CAACoC,IAAK;gBAChCoE,MAAM,EAAC,cAAc;gBACrBlG,UAAU,EAAE,KAAM;gBAClBmG,MAAM,EAAE;kBAAEC,CAAC,EAAE;gBAAc,CAAE;gBAC7BC,IAAI,EAAC,OAAO;gBACZrC,OAAO,EAAE,CACP;kBACEG,KAAK,EAAE,MAAM;kBACbC,SAAS,EAAE,cAAc;kBACzBjD,GAAG,EAAE,cAAc;kBACnBgE,KAAK,EAAE,MAAM;kBACbD,KAAK,EAAE;gBACT,CAAC,EACD;kBACEf,KAAK,EAAE,QAAQ;kBACf9C,QAAQ,EAAE,EAAA/C,qBAAA,GAAAoB,cAAc,CAAC4G,YAAY,cAAAhI,qBAAA,uBAA3BA,qBAAA,CAA6B2F,GAAG,CAAEsC,OAAe,KAAM;oBAC/DpC,KAAK,EAAEoC,OAAO;oBACdnC,SAAS,EAAEmC,OAAO;oBAClBpF,GAAG,EAAEoF,OAAO;oBACZrB,KAAK,EAAE,GAAG;oBACVb,MAAM,EAAGV,KAAa,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,cAAc,CAAC,CAAC,KAAI;kBACxD,CAAC,CAAC,CAAC,KAAI;gBACT,CAAC;cACD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGPlH,OAAA,CAACZ,IAAI;cACHkH,KAAK,EAAC,kGAAkB;cACxB3E,OAAO,EAAEM,eAAgB;cAAAuB,QAAA,EAExBzB,aAAa,iBACZ/B,OAAA,CAACb,KAAK;gBACJiJ,UAAU,EAAErG,aAAa,CAACkC,IAAK;gBAC/BoE,MAAM,EAAC,cAAc;gBACrBlG,UAAU,EAAE,KAAM;gBAClBmG,MAAM,EAAE;kBAAEC,CAAC,EAAE;gBAAc,CAAE;gBAC7BC,IAAI,EAAC,OAAO;gBACZrC,OAAO,EAAE,CACP;kBACEG,KAAK,EAAE,MAAM;kBACbC,SAAS,EAAE,cAAc;kBACzBjD,GAAG,EAAE,cAAc;kBACnBgE,KAAK,EAAE,MAAM;kBACbD,KAAK,EAAE;gBACT,CAAC,EACD;kBACEf,KAAK,EAAE,MAAM;kBACbC,SAAS,EAAE,UAAU;kBACrBjD,GAAG,EAAE,UAAU;kBACf+D,KAAK,EAAE,GAAG;kBACVb,MAAM,EAAGV,KAAa,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,cAAc,CAAC,CAAC,KAAI;gBACxD,CAAC,EACD;kBACEL,KAAK,EAAE,MAAM;kBACbC,SAAS,EAAE,aAAa;kBACxBjD,GAAG,EAAE,aAAa;kBAClB+D,KAAK,EAAE,GAAG;kBACVb,MAAM,EAAGV,KAAa,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,cAAc,CAAC,CAAC,KAAI;gBACxD,CAAC,EACD;kBACEL,KAAK,EAAE,QAAQ;kBACfC,SAAS,EAAE,eAAe;kBAC1BjD,GAAG,EAAE,eAAe;kBACpB+D,KAAK,EAAE,GAAG;kBACVb,MAAM,EAAGV,KAAa,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,cAAc,CAAC,CAAC,KAAI;gBACxD,CAAC,EACD,IAAI,EAAAjG,qBAAA,GAAAqB,aAAa,CAAC4G,YAAY,cAAAjI,qBAAA,uBAA1BA,qBAAA,CAA4B0F,GAAG,CAAEwC,MAAc,KAAM;kBACvDtC,KAAK,EAAEN,iBAAiB,CAACmB,MAAM,CAACyB,MAAM,CAAC,CAAC,IAAI,KAAKA,MAAM,EAAE;kBACzDrC,SAAS,EAAE,UAAUqC,MAAM,EAAE;kBAC7BtF,GAAG,EAAE,UAAUsF,MAAM,EAAE;kBACvBvB,KAAK,EAAE,GAAG;kBACVb,MAAM,EAAGV,KAAa,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEa,cAAc,CAAC,CAAC,KAAI;gBACxD,CAAC,CAAC,CAAC,KAAI,EAAE,CAAC;cACV;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GACJK,aAAa;UAAA;UACf;UACAvH,OAAA,CAAAE,SAAA;YAAAsD,QAAA,gBACExD,OAAA,CAACZ,IAAI;cACHkH,KAAK,EAAEiB,aAAa,CAAC3E,IAAK;cAC1BiG,KAAK,eACH7I,OAAA,CAACL,KAAK;gBAAA6D,QAAA,gBACJxD,OAAA,CAACX,MAAM;kBACLyG,KAAK,EAAE3E,eAAgB;kBACvB2H,QAAQ,EAAE1H,kBAAmB;kBAC7BoG,KAAK,EAAE;oBAAEH,KAAK,EAAE;kBAAI,CAAE;kBACtB0B,WAAW,EAAC,0BAAM;kBAAAvF,QAAA,gBAElBxD,OAAA,CAACM,MAAM;oBAAWwF,KAAK,EAAC,KAAK;oBAAAtC,QAAA,eAC3BxD,OAAA,CAACL,KAAK;sBAAA6D,QAAA,GAAC,0BAEL,eAAAxD,OAAA,CAACN,GAAG;wBAACsJ,KAAK,EAAC,MAAM;wBAAAxF,QAAA,GACdnC,QAAQ,CAACkE,MAAM,EAAC,qBACnB;sBAAA;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GANE,KAAK;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOT,CAAC,EACR7F,QAAQ,CAAC+E,GAAG,CAAC6C,OAAO,iBACnBjJ,OAAA,CAACM,MAAM;oBAAkBwF,KAAK,EAAEmD,OAAO,CAACC,EAAG;oBAAA1F,QAAA,eACzCxD,OAAA,CAACL,KAAK;sBAAA6D,QAAA,EACHyF,OAAO,CAACrG;oBAAI;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC,GAHG+B,OAAO,CAACC,EAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTlH,OAAA,CAACK,MAAM;kBACL0I,WAAW,EAAC,iBAAO;kBACnBI,UAAU;kBACVC,QAAQ,EAAEvD,YAAa;kBACvB2B,KAAK,EAAE;oBAAEH,KAAK,EAAE;kBAAI;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFlH,OAAA,CAACT,MAAM;kBAAC8J,IAAI,eAAErJ,OAAA,CAACJ,cAAc;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAACe,OAAO,EAAElC,aAAc;kBAAAvC,QAAA,EAAC;gBAE1D;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACR;cACDM,KAAK,EAAE;gBAAEW,YAAY,EAAE;cAAG,CAAE;cAAA3E,QAAA,eAE5BxD,OAAA;gBAAGwH,KAAK,EAAE;kBAAE8B,MAAM,EAAE,CAAC;kBAAEN,KAAK,EAAE;gBAAO,CAAE;gBAAAxF,QAAA,EAAE+D,aAAa,CAACgC;cAAW;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEPlH,OAAA,CAACb,KAAK;cACJgH,OAAO,EAAEF,eAAe,CAAC,CAAE;cAC3BmC,UAAU,EAAE7G,QAAS;cACrB8G,MAAM,EAAC,IAAI;cACX1G,OAAO,EAAEA,OAAQ;cACjBQ,UAAU,EAAE;gBACV,GAAGA,UAAU;gBACbqH,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAEA,CAACnH,KAAK,EAAEoH,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQpH,KAAK;cAC1C,CAAE;cACFuG,QAAQ,EAAEnD,iBAAkB;cAC5B2C,MAAM,EAAE;gBAAEC,CAAC,EAAE;cAAc,CAAE;cAC7BC,IAAI,EAAC;YAAO;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,eACF,CAAC,gBAEHlH,OAAA;YAAKwH,KAAK,EAAE;cAAEoC,SAAS,EAAE,QAAQ;cAAEhC,OAAO,EAAE;YAAO,CAAE;YAAApE,QAAA,gBACnDxD,OAAA,CAACP,IAAI;cAAC+I,IAAI,EAAC;YAAO;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrBlH,OAAA;cAAGwH,KAAK,EAAE;gBAAEqC,SAAS,EAAE;cAAG,CAAE;cAAArG,QAAA,EAAC;YAAM;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC1G,EAAA,CA7dID,WAAqB;EAAA,QACFV,eAAe;AAAA;AAAAiK,EAAA,GADlCvJ,WAAqB;AA+d3B,eAAeA,WAAW;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}