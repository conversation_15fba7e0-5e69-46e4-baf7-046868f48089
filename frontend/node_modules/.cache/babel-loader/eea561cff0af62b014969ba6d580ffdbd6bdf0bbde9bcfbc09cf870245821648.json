{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataUpdate.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, DatePicker, Button, Progress, Alert, message, Descriptions, Table, Space, Typography } from 'antd';\nimport { PlayCircleOutlined, ReloadOutlined, HistoryOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Title,\n  Text\n} = Typography;\nconst DataUpdate = () => {\n  _s();\n  var _dataRange$total_reco;\n  const [dateRange, setDateRange] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [currentTask, setCurrentTask] = useState(null);\n  const [dataRange, setDataRange] = useState(null);\n  const [history, setHistory] = useState([]);\n  const [pollingInterval, setPollingInterval] = useState(null);\n\n  // 获取当前数据范围\n  const fetchDataRange = async () => {\n    try {\n      const response = await api.get('/data-update/current-range');\n      if (response.data.success) {\n        setDataRange(response.data);\n      }\n    } catch (error) {\n      console.error('获取数据范围失败:', error);\n    }\n  };\n\n  // 获取历史记录\n  const fetchHistory = async () => {\n    try {\n      const response = await api.get('/data-update/history');\n      if (response.data.success) {\n        setHistory(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取历史记录失败:', error);\n    }\n  };\n\n  // 检查是否有正在运行的任务\n  const checkRunningTask = async () => {\n    try {\n      const response = await api.get('/data-update/running-task');\n      if (response.data.success && response.data.has_running_task) {\n        setCurrentTask(response.data.task);\n        startPolling(response.data.task.task_id);\n      }\n    } catch (error) {\n      console.error('检查运行任务失败:', error);\n    }\n  };\n\n  // 开始轮询任务状态\n  const startPolling = taskId => {\n    if (pollingInterval) {\n      clearInterval(pollingInterval);\n    }\n    const interval = setInterval(async () => {\n      try {\n        const response = await api.get(`/data-update/status/${taskId}`);\n        if (response.data.success) {\n          setCurrentTask(response.data);\n\n          // 如果任务完成，停止轮询\n          if (response.data.status === 'completed' || response.data.status === 'failed') {\n            clearInterval(interval);\n            setPollingInterval(null);\n            fetchHistory(); // 刷新历史记录\n            fetchDataRange(); // 刷新数据范围\n\n            if (response.data.status === 'completed') {\n              message.success('数据更新完成！');\n            } else {\n              message.error('数据更新失败！');\n            }\n          }\n        }\n      } catch (error) {\n        console.error('获取任务状态失败:', error);\n        clearInterval(interval);\n        setPollingInterval(null);\n      }\n    }, 2000);\n    setPollingInterval(interval);\n  };\n\n  // 启动数据更新\n  const handleStartUpdate = async () => {\n    if (!dateRange || !dateRange[0] || !dateRange[1]) {\n      message.error('请选择日期范围');\n      return;\n    }\n    const startDate = dateRange[0].format('YYYY-MM-DD');\n    const endDate = dateRange[1].format('YYYY-MM-DD');\n\n    // 验证日期范围\n    const daysDiff = dateRange[1].diff(dateRange[0], 'day');\n    if (daysDiff > 30) {\n      message.error('日期范围不能超过30天');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await api.post('/data-update/start', {\n        start_date: startDate,\n        end_date: endDate\n      });\n      if (response.data.success) {\n        message.success('数据更新任务已启动');\n        setCurrentTask({\n          task_id: response.data.task_id,\n          start_date: startDate,\n          end_date: endDate,\n          status: 'running',\n          total_accounts: response.data.total_accounts,\n          completed_accounts: 0,\n          current_step: '准备中...',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n          progress_percent: 0\n        });\n        startPolling(response.data.task_id);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '启动更新失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 历史记录表格列\n  const historyColumns = [{\n    title: '日期范围',\n    key: 'date_range',\n    render: record => `${record.start_date} 至 ${record.end_date}`\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => {\n      const statusMap = {\n        'running': {\n          text: '运行中',\n          color: 'blue'\n        },\n        'completed': {\n          text: '已完成',\n          color: 'green'\n        },\n        'failed': {\n          text: '失败',\n          color: 'red'\n        }\n      };\n      const config = statusMap[status] || {\n        text: status,\n        color: 'default'\n      };\n      return /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: config.color\n        },\n        children: config.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '账号数',\n    key: 'accounts',\n    render: record => `${record.completed_accounts}/${record.total_accounts}`\n  }, {\n    title: '进度',\n    dataIndex: 'progress_percent',\n    key: 'progress_percent',\n    render: percent => `${percent}%`\n  }, {\n    title: '耗时',\n    dataIndex: 'duration',\n    key: 'duration'\n  }, {\n    title: '开始时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: time => new Date(time).toLocaleString()\n  }];\n  useEffect(() => {\n    fetchDataRange();\n    fetchHistory();\n    checkRunningTask();\n    return () => {\n      if (pollingInterval) {\n        clearInterval(pollingInterval);\n      }\n    };\n  }, []);\n  const isTaskRunning = Boolean(currentTask && currentTask.status === 'running');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u6570\\u636E\\u66F4\\u65B0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5F53\\u524D\\u6570\\u636E\\u8303\\u56F4\",\n      style: {\n        marginBottom: 24\n      },\n      children: dataRange ? /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 3,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          children: dataRange.min_date || '无数据'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          children: dataRange.max_date || '无数据'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u603B\\u8BB0\\u5F55\\u6570\",\n          children: ((_dataRange$total_reco = dataRange.total_records) === null || _dataRange$total_reco === void 0 ? void 0 : _dataRange$total_reco.toLocaleString()) || 0\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u6700\\u540E\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: dataRange.last_update_time ? new Date(dataRange.last_update_time).toLocaleString() : '无'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u72B6\\u6001\",\n          children: dataRange.last_update_status === 'completed' ? '成功' : dataRange.last_update_status === 'failed' ? '失败' : '无'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Text, {\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u66F4\\u65B0\\u64CD\\u4F5C\",\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u65E5\\u671F\\u8303\\u56F4\\uFF08\\u6700\\u957F30\\u5929\\uFF09\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n            value: dateRange,\n            onChange: setDateRange,\n            disabled: isTaskRunning,\n            style: {\n              marginLeft: 16\n            },\n            disabledDate: current => {\n              // 禁用未来日期\n              return current && current > dayjs().endOf('day');\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 19\n          }, this),\n          onClick: handleStartUpdate,\n          loading: loading,\n          disabled: isTaskRunning,\n          size: \"large\",\n          children: \"\\u5F00\\u59CB\\u66F4\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), isTaskRunning && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6CE8\\u610F\",\n          description: \"\\u6570\\u636E\\u66F4\\u65B0\\u8FC7\\u7A0B\\u4E2D\\u4F1A\\u5148\\u6E05\\u7A7A\\u73B0\\u6709\\u6570\\u636E\\uFF0C\\u7136\\u540E\\u91CD\\u65B0\\u4E0B\\u8F7D\\u6240\\u6709\\u8D26\\u53F7\\u7684\\u6570\\u636E\\u3002\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), currentTask && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5F53\\u524D\\u4EFB\\u52A1\\u8FDB\\u5EA6\",\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Progress, {\n          percent: currentTask.progress_percent,\n          status: currentTask.status === 'failed' ? 'exception' : 'active',\n          strokeColor: currentTask.status === 'completed' ? '#52c41a' : undefined\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions, {\n          column: 2,\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u65E5\\u671F\\u8303\\u56F4\",\n            children: [currentTask.start_date, \" \\u81F3 \", currentTask.end_date]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u72B6\\u6001\",\n            children: currentTask.status === 'running' ? '运行中' : currentTask.status === 'completed' ? '已完成' : '失败'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F53\\u524D\\u8D26\\u53F7\",\n            children: currentTask.current_account_name || '-'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F53\\u524D\\u6B65\\u9AA4\",\n            children: currentTask.current_step || '-'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u8FDB\\u5EA6\",\n            children: [currentTask.completed_accounts, \"/\", currentTask.total_accounts, \" \\u4E2A\\u8D26\\u53F7\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F00\\u59CB\\u65F6\\u95F4\",\n            children: new Date(currentTask.created_at).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this), currentTask.error_message && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u9519\\u8BEF\\u4FE1\\u606F\",\n          description: currentTask.error_message,\n          type: \"error\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), \"\\u5386\\u53F2\\u66F4\\u65B0\\u8BB0\\u5F55\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 19\n        }, this),\n        onClick: () => {\n          fetchHistory();\n          fetchDataRange();\n        },\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: historyColumns,\n        dataSource: history,\n        rowKey: \"id\",\n        pagination: {\n          pageSize: 10\n        },\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 5\n  }, this);\n};\n_s(DataUpdate, \"i3V1wRjxs323bIf42ZlvkfgsIw4=\");\n_c = DataUpdate;\nexport default DataUpdate;\nvar _c;\n$RefreshReg$(_c, \"DataUpdate\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "DatePicker", "<PERSON><PERSON>", "Progress", "<PERSON><PERSON>", "message", "Descriptions", "Table", "Space", "Typography", "PlayCircleOutlined", "ReloadOutlined", "HistoryOutlined", "dayjs", "api", "jsxDEV", "_jsxDEV", "RangePicker", "Title", "Text", "DataUpdate", "_s", "_dataRange$total_reco", "date<PERSON><PERSON><PERSON>", "setDateRange", "loading", "setLoading", "currentTask", "setCurrentTask", "dataRange", "setDataRange", "history", "setHistory", "pollingInterval", "setPollingInterval", "fetchDataRange", "response", "get", "data", "success", "error", "console", "fetchHistory", "checkRunningTask", "has_running_task", "task", "startPolling", "task_id", "taskId", "clearInterval", "interval", "setInterval", "status", "handleStartUpdate", "startDate", "format", "endDate", "daysDiff", "diff", "post", "start_date", "end_date", "total_accounts", "completed_accounts", "current_step", "created_at", "Date", "toISOString", "updated_at", "progress_percent", "_error$response", "_error$response$data", "detail", "historyColumns", "title", "key", "render", "record", "dataIndex", "statusMap", "text", "color", "config", "style", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "percent", "time", "toLocaleString", "isTaskRunning", "Boolean", "padding", "level", "marginBottom", "column", "<PERSON><PERSON>", "label", "min_date", "max_date", "total_records", "last_update_time", "last_update_status", "direction", "width", "strong", "value", "onChange", "disabled", "marginLeft", "disabledDate", "current", "endOf", "type", "icon", "onClick", "size", "description", "showIcon", "strokeColor", "undefined", "current_account_name", "error_message", "extra", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataUpdate.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, DatePicker, Button, Progress, Alert, message, \n  Descriptions, Table, Space, Typography, Divider \n} from 'antd';\nimport { PlayCircleOutlined, ReloadOutlined, HistoryOutlined } from '@ant-design/icons';\nimport dayjs, { Dayjs } from 'dayjs';\nimport api from '../services/api';\n\nconst { RangePicker } = DatePicker;\nconst { Title, Text } = Typography;\n\ninterface UpdateTask {\n  task_id: number;\n  start_date: string;\n  end_date: string;\n  status: string;\n  total_accounts: number;\n  completed_accounts: number;\n  current_account_name?: string;\n  current_step?: string;\n  error_message?: string;\n  created_at: string;\n  updated_at: string;\n  completed_at?: string;\n  progress_percent: number;\n}\n\ninterface DataRange {\n  min_date?: string;\n  max_date?: string;\n  total_records: number;\n  last_update_time?: string;\n  last_update_status?: string;\n}\n\nconst DataUpdate: React.FC = () => {\n  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [currentTask, setCurrentTask] = useState<UpdateTask | null>(null);\n  const [dataRange, setDataRange] = useState<DataRange | null>(null);\n  const [history, setHistory] = useState<any[]>([]);\n  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);\n\n  // 获取当前数据范围\n  const fetchDataRange = async () => {\n    try {\n      const response = await api.get('/data-update/current-range');\n      if (response.data.success) {\n        setDataRange(response.data);\n      }\n    } catch (error) {\n      console.error('获取数据范围失败:', error);\n    }\n  };\n\n  // 获取历史记录\n  const fetchHistory = async () => {\n    try {\n      const response = await api.get('/data-update/history');\n      if (response.data.success) {\n        setHistory(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取历史记录失败:', error);\n    }\n  };\n\n  // 检查是否有正在运行的任务\n  const checkRunningTask = async () => {\n    try {\n      const response = await api.get('/data-update/running-task');\n      if (response.data.success && response.data.has_running_task) {\n        setCurrentTask(response.data.task);\n        startPolling(response.data.task.task_id);\n      }\n    } catch (error) {\n      console.error('检查运行任务失败:', error);\n    }\n  };\n\n  // 开始轮询任务状态\n  const startPolling = (taskId: number) => {\n    if (pollingInterval) {\n      clearInterval(pollingInterval);\n    }\n\n    const interval = setInterval(async () => {\n      try {\n        const response = await api.get(`/data-update/status/${taskId}`);\n        if (response.data.success) {\n          setCurrentTask(response.data);\n          \n          // 如果任务完成，停止轮询\n          if (response.data.status === 'completed' || response.data.status === 'failed') {\n            clearInterval(interval);\n            setPollingInterval(null);\n            fetchHistory(); // 刷新历史记录\n            fetchDataRange(); // 刷新数据范围\n            \n            if (response.data.status === 'completed') {\n              message.success('数据更新完成！');\n            } else {\n              message.error('数据更新失败！');\n            }\n          }\n        }\n      } catch (error) {\n        console.error('获取任务状态失败:', error);\n        clearInterval(interval);\n        setPollingInterval(null);\n      }\n    }, 2000);\n\n    setPollingInterval(interval);\n  };\n\n  // 启动数据更新\n  const handleStartUpdate = async () => {\n    if (!dateRange || !dateRange[0] || !dateRange[1]) {\n      message.error('请选择日期范围');\n      return;\n    }\n\n    const startDate = dateRange[0].format('YYYY-MM-DD');\n    const endDate = dateRange[1].format('YYYY-MM-DD');\n\n    // 验证日期范围\n    const daysDiff = dateRange[1].diff(dateRange[0], 'day');\n    if (daysDiff > 30) {\n      message.error('日期范围不能超过30天');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await api.post('/data-update/start', {\n        start_date: startDate,\n        end_date: endDate\n      });\n\n      if (response.data.success) {\n        message.success('数据更新任务已启动');\n        setCurrentTask({\n          task_id: response.data.task_id,\n          start_date: startDate,\n          end_date: endDate,\n          status: 'running',\n          total_accounts: response.data.total_accounts,\n          completed_accounts: 0,\n          current_step: '准备中...',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n          progress_percent: 0\n        });\n        \n        startPolling(response.data.task_id);\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '启动更新失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 历史记录表格列\n  const historyColumns = [\n    {\n      title: '日期范围',\n      key: 'date_range',\n      render: (record: any) => `${record.start_date} 至 ${record.end_date}`\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => {\n        const statusMap = {\n          'running': { text: '运行中', color: 'blue' },\n          'completed': { text: '已完成', color: 'green' },\n          'failed': { text: '失败', color: 'red' }\n        };\n        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };\n        return <Text style={{ color: config.color }}>{config.text}</Text>;\n      }\n    },\n    {\n      title: '账号数',\n      key: 'accounts',\n      render: (record: any) => `${record.completed_accounts}/${record.total_accounts}`\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress_percent',\n      key: 'progress_percent',\n      render: (percent: number) => `${percent}%`\n    },\n    {\n      title: '耗时',\n      dataIndex: 'duration',\n      key: 'duration'\n    },\n    {\n      title: '开始时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (time: string) => new Date(time).toLocaleString()\n    }\n  ];\n\n  useEffect(() => {\n    fetchDataRange();\n    fetchHistory();\n    checkRunningTask();\n\n    return () => {\n      if (pollingInterval) {\n        clearInterval(pollingInterval);\n      }\n    };\n  }, []);\n\n  const isTaskRunning = Boolean(currentTask && currentTask.status === 'running');\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>数据更新</Title>\n      \n      {/* 当前数据范围 */}\n      <Card title=\"当前数据范围\" style={{ marginBottom: 24 }}>\n        {dataRange ? (\n          <Descriptions column={3}>\n            <Descriptions.Item label=\"开始日期\">\n              {dataRange.min_date || '无数据'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"结束日期\">\n              {dataRange.max_date || '无数据'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"总记录数\">\n              {dataRange.total_records?.toLocaleString() || 0}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"最后更新时间\">\n              {dataRange.last_update_time ? new Date(dataRange.last_update_time).toLocaleString() : '无'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新状态\">\n              {dataRange.last_update_status === 'completed' ? '成功' :\n               dataRange.last_update_status === 'failed' ? '失败' : '无'}\n            </Descriptions.Item>\n          </Descriptions>\n        ) : (\n          <Text>加载中...</Text>\n        )}\n      </Card>\n\n      {/* 数据更新操作 */}\n      <Card title=\"数据更新操作\" style={{ marginBottom: 24 }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择日期范围（最长30天）：</Text>\n            <RangePicker\n              value={dateRange}\n              onChange={setDateRange}\n              disabled={isTaskRunning}\n              style={{ marginLeft: 16 }}\n              disabledDate={(current) => {\n                // 禁用未来日期\n                return current && current > dayjs().endOf('day');\n              }}\n            />\n          </div>\n          \n          <Button\n            type=\"primary\"\n            icon={<PlayCircleOutlined />}\n            onClick={handleStartUpdate}\n            loading={loading}\n            disabled={isTaskRunning}\n            size=\"large\"\n          >\n            开始更新\n          </Button>\n\n          {isTaskRunning && (\n            <Alert\n              message=\"注意\"\n              description=\"数据更新过程中会先清空现有数据，然后重新下载所有账号的数据。请耐心等待。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 当前任务进度 */}\n      {currentTask && (\n        <Card title=\"当前任务进度\" style={{ marginBottom: 24 }}>\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <Progress \n              percent={currentTask.progress_percent} \n              status={currentTask.status === 'failed' ? 'exception' : 'active'}\n              strokeColor={currentTask.status === 'completed' ? '#52c41a' : undefined}\n            />\n            \n            <Descriptions column={2}>\n              <Descriptions.Item label=\"日期范围\">\n                {currentTask.start_date} 至 {currentTask.end_date}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"状态\">\n                {currentTask.status === 'running' ? '运行中' : \n                 currentTask.status === 'completed' ? '已完成' : '失败'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"当前账号\">\n                {currentTask.current_account_name || '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"当前步骤\">\n                {currentTask.current_step || '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"进度\">\n                {currentTask.completed_accounts}/{currentTask.total_accounts} 个账号\n              </Descriptions.Item>\n              <Descriptions.Item label=\"开始时间\">\n                {new Date(currentTask.created_at).toLocaleString()}\n              </Descriptions.Item>\n            </Descriptions>\n\n            {currentTask.error_message && (\n              <Alert\n                message=\"错误信息\"\n                description={currentTask.error_message}\n                type=\"error\"\n                showIcon\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* 历史记录 */}\n      <Card \n        title={\n          <Space>\n            <HistoryOutlined />\n            历史更新记录\n          </Space>\n        }\n        extra={\n          <Button \n            icon={<ReloadOutlined />} \n            onClick={() => { fetchHistory(); fetchDataRange(); }}\n          >\n            刷新\n          </Button>\n        }\n      >\n        <Table\n          columns={historyColumns}\n          dataSource={history}\n          rowKey=\"id\"\n          pagination={{ pageSize: 10 }}\n          size=\"small\"\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default DataUpdate;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAClDC,YAAY,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,QACjC,MAAM;AACb,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,eAAe,QAAQ,mBAAmB;AACvF,OAAOC,KAAK,MAAiB,OAAO;AACpC,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAY,CAAC,GAAGhB,UAAU;AAClC,MAAM;EAAEiB,KAAK;EAAEC;AAAK,CAAC,GAAGV,UAAU;AA0BlC,MAAMW,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAsC,IAAI,CAAC;EACrF,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAoB,IAAI,CAAC;EACvE,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAmB,IAAI,CAAC;EAClE,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAQ,EAAE,CAAC;EACjD,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAwB,IAAI,CAAC;;EAEnF;EACA,MAAMqC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,4BAA4B,CAAC;MAC5D,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBT,YAAY,CAACM,QAAQ,CAACE,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,sBAAsB,CAAC;MACtD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBP,UAAU,CAACI,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAChC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,2BAA2B,CAAC;MAC3D,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACM,gBAAgB,EAAE;QAC3DhB,cAAc,CAACQ,QAAQ,CAACE,IAAI,CAACO,IAAI,CAAC;QAClCC,YAAY,CAACV,QAAQ,CAACE,IAAI,CAACO,IAAI,CAACE,OAAO,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMM,YAAY,GAAIE,MAAc,IAAK;IACvC,IAAIf,eAAe,EAAE;MACnBgB,aAAa,CAAChB,eAAe,CAAC;IAChC;IAEA,MAAMiB,QAAQ,GAAGC,WAAW,CAAC,YAAY;MACvC,IAAI;QACF,MAAMf,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,uBAAuBW,MAAM,EAAE,CAAC;QAC/D,IAAIZ,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBX,cAAc,CAACQ,QAAQ,CAACE,IAAI,CAAC;;UAE7B;UACA,IAAIF,QAAQ,CAACE,IAAI,CAACc,MAAM,KAAK,WAAW,IAAIhB,QAAQ,CAACE,IAAI,CAACc,MAAM,KAAK,QAAQ,EAAE;YAC7EH,aAAa,CAACC,QAAQ,CAAC;YACvBhB,kBAAkB,CAAC,IAAI,CAAC;YACxBQ,YAAY,CAAC,CAAC,CAAC,CAAC;YAChBP,cAAc,CAAC,CAAC,CAAC,CAAC;;YAElB,IAAIC,QAAQ,CAACE,IAAI,CAACc,MAAM,KAAK,WAAW,EAAE;cACxC/C,OAAO,CAACkC,OAAO,CAAC,SAAS,CAAC;YAC5B,CAAC,MAAM;cACLlC,OAAO,CAACmC,KAAK,CAAC,SAAS,CAAC;YAC1B;UACF;QACF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCS,aAAa,CAACC,QAAQ,CAAC;QACvBhB,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC,EAAE,IAAI,CAAC;IAERA,kBAAkB,CAACgB,QAAQ,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC9B,SAAS,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EAAE;MAChDlB,OAAO,CAACmC,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,MAAMc,SAAS,GAAG/B,SAAS,CAAC,CAAC,CAAC,CAACgC,MAAM,CAAC,YAAY,CAAC;IACnD,MAAMC,OAAO,GAAGjC,SAAS,CAAC,CAAC,CAAC,CAACgC,MAAM,CAAC,YAAY,CAAC;;IAEjD;IACA,MAAME,QAAQ,GAAGlC,SAAS,CAAC,CAAC,CAAC,CAACmC,IAAI,CAACnC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;IACvD,IAAIkC,QAAQ,GAAG,EAAE,EAAE;MACjBpD,OAAO,CAACmC,KAAK,CAAC,aAAa,CAAC;MAC5B;IACF;IAEAd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMtB,GAAG,CAAC6C,IAAI,CAAC,oBAAoB,EAAE;QACpDC,UAAU,EAAEN,SAAS;QACrBO,QAAQ,EAAEL;MACZ,CAAC,CAAC;MAEF,IAAIpB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBlC,OAAO,CAACkC,OAAO,CAAC,WAAW,CAAC;QAC5BX,cAAc,CAAC;UACbmB,OAAO,EAAEX,QAAQ,CAACE,IAAI,CAACS,OAAO;UAC9Ba,UAAU,EAAEN,SAAS;UACrBO,QAAQ,EAAEL,OAAO;UACjBJ,MAAM,EAAE,SAAS;UACjBU,cAAc,EAAE1B,QAAQ,CAACE,IAAI,CAACwB,cAAc;UAC5CC,kBAAkB,EAAE,CAAC;UACrBC,YAAY,EAAE,QAAQ;UACtBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCE,gBAAgB,EAAE;QACpB,CAAC,CAAC;QAEFvB,YAAY,CAACV,QAAQ,CAACE,IAAI,CAACS,OAAO,CAAC;MACrC;IACF,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAA8B,eAAA,EAAAC,oBAAA;MACnBlE,OAAO,CAACmC,KAAK,CAAC,EAAA8B,eAAA,GAAA9B,KAAK,CAACJ,QAAQ,cAAAkC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhC,IAAI,cAAAiC,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,QAAQ,CAAC;IACzD,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+C,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGC,MAAW,IAAK,GAAGA,MAAM,CAACjB,UAAU,MAAMiB,MAAM,CAAChB,QAAQ;EACpE,CAAC,EACD;IACEa,KAAK,EAAE,IAAI;IACXI,SAAS,EAAE,QAAQ;IACnBH,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGxB,MAAc,IAAK;MAC1B,MAAM2B,SAAS,GAAG;QAChB,SAAS,EAAE;UAAEC,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAO,CAAC;QACzC,WAAW,EAAE;UAAED,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAQ,CAAC;QAC5C,QAAQ,EAAE;UAAED,IAAI,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAM;MACvC,CAAC;MACD,MAAMC,MAAM,GAAGH,SAAS,CAAC3B,MAAM,CAA2B,IAAI;QAAE4B,IAAI,EAAE5B,MAAM;QAAE6B,KAAK,EAAE;MAAU,CAAC;MAChG,oBAAOjE,OAAA,CAACG,IAAI;QAACgE,KAAK,EAAE;UAAEF,KAAK,EAAEC,MAAM,CAACD;QAAM,CAAE;QAAAG,QAAA,EAAEF,MAAM,CAACF;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IACnE;EACF,CAAC,EACD;IACEd,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,MAAW,IAAK,GAAGA,MAAM,CAACd,kBAAkB,IAAIc,MAAM,CAACf,cAAc;EAChF,CAAC,EACD;IACEY,KAAK,EAAE,IAAI;IACXI,SAAS,EAAE,kBAAkB;IAC7BH,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAGa,OAAe,IAAK,GAAGA,OAAO;EACzC,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXI,SAAS,EAAE,UAAU;IACrBH,GAAG,EAAE;EACP,CAAC,EACD;IACED,KAAK,EAAE,MAAM;IACbI,SAAS,EAAE,YAAY;IACvBH,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGc,IAAY,IAAK,IAAIxB,IAAI,CAACwB,IAAI,CAAC,CAACC,cAAc,CAAC;EAC1D,CAAC,CACF;EAED5F,SAAS,CAAC,MAAM;IACdoC,cAAc,CAAC,CAAC;IAChBO,YAAY,CAAC,CAAC;IACdC,gBAAgB,CAAC,CAAC;IAElB,OAAO,MAAM;MACX,IAAIV,eAAe,EAAE;QACnBgB,aAAa,CAAChB,eAAe,CAAC;MAChC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2D,aAAa,GAAGC,OAAO,CAAClE,WAAW,IAAIA,WAAW,CAACyB,MAAM,KAAK,SAAS,CAAC;EAE9E,oBACEpC,OAAA;IAAKmE,KAAK,EAAE;MAAEW,OAAO,EAAE;IAAO,CAAE;IAAAV,QAAA,gBAC9BpE,OAAA,CAACE,KAAK;MAAC6E,KAAK,EAAE,CAAE;MAAAX,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BxE,OAAA,CAAChB,IAAI;MAAC0E,KAAK,EAAC,sCAAQ;MAACS,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAG,CAAE;MAAAZ,QAAA,EAC9CvD,SAAS,gBACRb,OAAA,CAACV,YAAY;QAAC2F,MAAM,EAAE,CAAE;QAAAb,QAAA,gBACtBpE,OAAA,CAACV,YAAY,CAAC4F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAf,QAAA,EAC5BvD,SAAS,CAACuE,QAAQ,IAAI;QAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAf,QAAA,EAC5BvD,SAAS,CAACwE,QAAQ,IAAI;QAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAf,QAAA,EAC5B,EAAA9D,qBAAA,GAAAO,SAAS,CAACyE,aAAa,cAAAhF,qBAAA,uBAAvBA,qBAAA,CAAyBqE,cAAc,CAAC,CAAC,KAAI;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;UAACC,KAAK,EAAC,sCAAQ;UAAAf,QAAA,EAC9BvD,SAAS,CAAC0E,gBAAgB,GAAG,IAAIrC,IAAI,CAACrC,SAAS,CAAC0E,gBAAgB,CAAC,CAACZ,cAAc,CAAC,CAAC,GAAG;QAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAf,QAAA,EAC5BvD,SAAS,CAAC2E,kBAAkB,KAAK,WAAW,GAAG,IAAI,GACnD3E,SAAS,CAAC2E,kBAAkB,KAAK,QAAQ,GAAG,IAAI,GAAG;QAAG;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,gBAEfxE,OAAA,CAACG,IAAI;QAAAiE,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IACnB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPxE,OAAA,CAAChB,IAAI;MAAC0E,KAAK,EAAC,sCAAQ;MAACS,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAG,CAAE;MAAAZ,QAAA,eAC/CpE,OAAA,CAACR,KAAK;QAACiG,SAAS,EAAC,UAAU;QAACtB,KAAK,EAAE;UAAEuB,KAAK,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBACnDpE,OAAA;UAAAoE,QAAA,gBACEpE,OAAA,CAACG,IAAI;YAACwF,MAAM;YAAAvB,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCxE,OAAA,CAACC,WAAW;YACV2F,KAAK,EAAErF,SAAU;YACjBsF,QAAQ,EAAErF,YAAa;YACvBsF,QAAQ,EAAElB,aAAc;YACxBT,KAAK,EAAE;cAAE4B,UAAU,EAAE;YAAG,CAAE;YAC1BC,YAAY,EAAGC,OAAO,IAAK;cACzB;cACA,OAAOA,OAAO,IAAIA,OAAO,GAAGpG,KAAK,CAAC,CAAC,CAACqG,KAAK,CAAC,KAAK,CAAC;YAClD;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxE,OAAA,CAACd,MAAM;UACLiH,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEpG,OAAA,CAACN,kBAAkB;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7B6B,OAAO,EAAEhE,iBAAkB;UAC3B5B,OAAO,EAAEA,OAAQ;UACjBqF,QAAQ,EAAElB,aAAc;UACxB0B,IAAI,EAAC,OAAO;UAAAlC,QAAA,EACb;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERI,aAAa,iBACZ5E,OAAA,CAACZ,KAAK;UACJC,OAAO,EAAC,cAAI;UACZkH,WAAW,EAAC,0NAAsC;UAClDJ,IAAI,EAAC,SAAS;UACdK,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGN7D,WAAW,iBACVX,OAAA,CAAChB,IAAI;MAAC0E,KAAK,EAAC,sCAAQ;MAACS,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAG,CAAE;MAAAZ,QAAA,eAC/CpE,OAAA,CAACR,KAAK;QAACiG,SAAS,EAAC,UAAU;QAACtB,KAAK,EAAE;UAAEuB,KAAK,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBACnDpE,OAAA,CAACb,QAAQ;UACPsF,OAAO,EAAE9D,WAAW,CAAC0C,gBAAiB;UACtCjB,MAAM,EAAEzB,WAAW,CAACyB,MAAM,KAAK,QAAQ,GAAG,WAAW,GAAG,QAAS;UACjEqE,WAAW,EAAE9F,WAAW,CAACyB,MAAM,KAAK,WAAW,GAAG,SAAS,GAAGsE;QAAU;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eAEFxE,OAAA,CAACV,YAAY;UAAC2F,MAAM,EAAE,CAAE;UAAAb,QAAA,gBACtBpE,OAAA,CAACV,YAAY,CAAC4F,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAf,QAAA,GAC5BzD,WAAW,CAACiC,UAAU,EAAC,UAAG,EAACjC,WAAW,CAACkC,QAAQ;UAAA;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;YAACC,KAAK,EAAC,cAAI;YAAAf,QAAA,EAC1BzD,WAAW,CAACyB,MAAM,KAAK,SAAS,GAAG,KAAK,GACxCzB,WAAW,CAACyB,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG;UAAI;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAf,QAAA,EAC5BzD,WAAW,CAACgG,oBAAoB,IAAI;UAAG;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAf,QAAA,EAC5BzD,WAAW,CAACqC,YAAY,IAAI;UAAG;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;YAACC,KAAK,EAAC,cAAI;YAAAf,QAAA,GAC1BzD,WAAW,CAACoC,kBAAkB,EAAC,GAAC,EAACpC,WAAW,CAACmC,cAAc,EAAC,qBAC/D;UAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eACpBxE,OAAA,CAACV,YAAY,CAAC4F,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAf,QAAA,EAC5B,IAAIlB,IAAI,CAACvC,WAAW,CAACsC,UAAU,CAAC,CAAC0B,cAAc,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEd7D,WAAW,CAACiG,aAAa,iBACxB5G,OAAA,CAACZ,KAAK;UACJC,OAAO,EAAC,0BAAM;UACdkH,WAAW,EAAE5F,WAAW,CAACiG,aAAc;UACvCT,IAAI,EAAC,OAAO;UACZK,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP,eAGDxE,OAAA,CAAChB,IAAI;MACH0E,KAAK,eACH1D,OAAA,CAACR,KAAK;QAAA4E,QAAA,gBACJpE,OAAA,CAACJ,eAAe;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wCAErB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MACDqC,KAAK,eACH7G,OAAA,CAACd,MAAM;QACLkH,IAAI,eAAEpG,OAAA,CAACL,cAAc;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzB6B,OAAO,EAAEA,CAAA,KAAM;UAAE3E,YAAY,CAAC,CAAC;UAAEP,cAAc,CAAC,CAAC;QAAE,CAAE;QAAAiD,QAAA,EACtD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAJ,QAAA,eAEDpE,OAAA,CAACT,KAAK;QACJuH,OAAO,EAAErD,cAAe;QACxBsD,UAAU,EAAEhG,OAAQ;QACpBiG,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAC7BZ,IAAI,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnE,EAAA,CAxUID,UAAoB;AAAA+G,EAAA,GAApB/G,UAAoB;AA0U1B,eAAeA,UAAU;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}