{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Sider,\n  Content\n} = Layout;\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst DataDetails = () => {\n  _s();\n  const [selectedPlatform, setSelectedPlatform] = useState('wechat_mp');\n  const [selectedDataType, setSelectedDataType] = useState('content_trend');\n  const [selectedAccount, setSelectedAccount] = useState('all');\n  const [accounts, setAccounts] = useState([]);\n  const [dataList, setDataList] = useState([]);\n  const [dataConfig, setDataConfig] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [accountSummary, setAccountSummary] = useState(null);\n  const [growthSummary, setGrowthSummary] = useState(null);\n  const [overviewLoading, setOverviewLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 20,\n    total: 0\n  });\n  const [searchText, setSearchText] = useState('');\n\n  // 平台配置\n  const platformConfig = {\n    wechat_mp: {\n      name: '微信公众号',\n      dataTypes: {\n        content_trend: '内容数据趋势明细',\n        content_source: '内容流量来源明细',\n        content_detail: '内容已通知内容明细',\n        user_channel: '用户增长明细',\n        user_source: '用户来源明细'\n      }\n    },\n    wechat_channels: {\n      name: '视频号',\n      dataTypes: {}\n    },\n    xiaohongshu: {\n      name: '小红书',\n      dataTypes: {}\n    }\n  };\n\n  // 菜单项\n  const menuItems = [{\n    key: 'wechat_mp',\n    label: '微信公众号',\n    children: [{\n      key: 'wechat_mp_overview',\n      label: '总览'\n    }, {\n      key: 'wechat_mp_content_trend',\n      label: '内容数据趋势明细'\n    }, {\n      key: 'wechat_mp_content_source',\n      label: '内容流量来源明细'\n    }, {\n      key: 'wechat_mp_content_detail',\n      label: '内容已通知内容明细'\n    }, {\n      key: 'wechat_mp_user_channel',\n      label: '用户增长明细'\n    }, {\n      key: 'wechat_mp_user_source',\n      label: '用户来源明细'\n    }]\n  }, {\n    key: 'wechat_channels',\n    label: '视频号',\n    disabled: true,\n    children: [{\n      key: 'wechat_channels_placeholder',\n      label: '敬请期待',\n      disabled: true\n    }]\n  }, {\n    key: 'xiaohongshu',\n    label: '小红书',\n    disabled: true,\n    children: [{\n      key: 'xiaohongshu_placeholder',\n      label: '敬请期待',\n      disabled: true\n    }]\n  }];\n  useEffect(() => {\n    fetchDataConfig();\n    fetchAccounts();\n  }, []);\n  useEffect(() => {\n    if (selectedAccount && selectedDataType) {\n      if (selectedDataType === 'overview') {\n        fetchOverviewData();\n      } else {\n        fetchDataList();\n      }\n    }\n  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);\n  const fetchDataConfig = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/config');\n      if (response.data.success) {\n        setDataConfig(response.data.data_types);\n      }\n    } catch (error) {\n      console.error('获取数据配置失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取数据配置失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取数据配置失败');\n      }\n    }\n  };\n  const fetchAccounts = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/accounts');\n      if (response.data.success) {\n        setAccounts(response.data.accounts);\n        // 默认选择\"全部\"\n        setSelectedAccount('all');\n      }\n    } catch (error) {\n      console.error('获取账号列表失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取账号列表失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取账号列表失败');\n      }\n    }\n  };\n  const fetchDataList = async () => {\n    if (!selectedAccount || !selectedDataType) return;\n    setLoading(true);\n    try {\n      const params = {\n        page: pagination.current,\n        page_size: pagination.pageSize,\n        search: searchText || undefined,\n        sort_field: 'created_at',\n        sort_order: 'desc'\n      };\n\n      // 只有当选择了具体账号时才传递 account_id 参数\n      if (selectedAccount !== 'all') {\n        params.account_id = selectedAccount;\n      }\n      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, {\n        params\n      });\n      if (response.data.success) {\n        setDataList(response.data.data);\n        setPagination(prev => ({\n          ...prev,\n          total: response.data.total,\n          current: response.data.page\n        }));\n      } else {\n        message.error(response.data.error || '获取数据失败');\n      }\n    } catch (error) {\n      message.error('获取数据列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMenuClick = ({\n    key\n  }) => {\n    const parts = key.split('_');\n    if (parts.length >= 3) {\n      const platform = parts.slice(0, 2).join('_'); // wechat_mp\n      const dataType = parts.slice(2).join('_'); // content_trend\n\n      setSelectedPlatform(platform);\n      setSelectedDataType(dataType);\n      setPagination(prev => ({\n        ...prev,\n        current: 1\n      }));\n    }\n  };\n  const handleTableChange = paginationInfo => {\n    setPagination(prev => ({\n      ...prev,\n      current: paginationInfo.current,\n      pageSize: paginationInfo.pageSize\n    }));\n  };\n  const handleSearch = value => {\n    setSearchText(value);\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleRefresh = () => {\n    fetchDataList();\n  };\n\n  // 用户来源映射\n  const userSourceMapping = {\n    \"0\": \"其他合计\",\n    \"1\": \"公众号搜索\",\n    \"17\": \"名片分享\",\n    \"30\": \"扫描二维码\",\n    \"57\": \"文章内账号名称\",\n    \"100\": \"微信广告\",\n    \"161\": \"他人转载\",\n    \"149\": \"小程序关注\",\n    \"200\": \"视频号\",\n    \"201\": \"直播\"\n  };\n\n  // 生成表格列配置\n  const generateColumns = () => {\n    const config = dataConfig[selectedDataType];\n    if (!config) return [];\n    return config.columns.map(col => ({\n      title: col.title,\n      dataIndex: col.key,\n      key: col.key,\n      render: value => {\n        if (col.type === 'date') {\n          return value ? new Date(value).toLocaleDateString() : '-';\n        }\n        if (col.type === 'datetime') {\n          return value ? new Date(value).toLocaleString() : '-';\n        }\n        if (col.type === 'number') {\n          return typeof value === 'number' ? value.toLocaleString() : value || 0;\n        }\n        if (col.type === 'url') {\n          return value ? /*#__PURE__*/_jsxDEV(\"a\", {\n            href: value,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"\\u67E5\\u770B\\u94FE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this) : '-';\n        }\n        if (col.type === 'user_source') {\n          return userSourceMapping[String(value)] || `未知来源(${value})`;\n        }\n        return value || '-';\n      },\n      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',\n      width: col.key === 'account_name' ? 150 : col.key === 'updated_at' ? 180 : col.type === 'text' ? 200 : 120,\n      fixed: col.key === 'account_name' ? 'left' : col.key === 'updated_at' ? 'right' : undefined\n    }));\n  };\n  const currentConfig = dataConfig[selectedDataType];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh'\n    },\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        height: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        width: 250,\n        theme: \"light\",\n        style: {\n          borderRight: '1px solid #f0f0f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #f0f0f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u6570\\u636E\\u660E\\u7EC6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          mode: \"inline\",\n          selectedKeys: [`${selectedPlatform}_${selectedDataType}`],\n          items: menuItems,\n          onClick: handleMenuClick,\n          style: {\n            height: 'calc(100% - 64px)',\n            borderRight: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Content, {\n          style: {\n            padding: '24px',\n            background: '#fff'\n          },\n          children: [currentConfig && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              title: currentConfig.name,\n              extra: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: selectedAccount,\n                  onChange: setSelectedAccount,\n                  style: {\n                    width: 200\n                  },\n                  placeholder: \"\\u9009\\u62E9\\u8D26\\u53F7\",\n                  children: [/*#__PURE__*/_jsxDEV(Option, {\n                    value: \"all\",\n                    children: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [\"\\u5168\\u90E8\\u8D26\\u53F7\", /*#__PURE__*/_jsxDEV(Tag, {\n                        color: \"blue\",\n                        children: [accounts.length, \" \\u4E2A\\u8D26\\u53F7\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 27\n                    }, this)\n                  }, \"all\", false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 25\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(Option, {\n                    value: account.id,\n                    children: /*#__PURE__*/_jsxDEV(Space, {\n                      children: account.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 29\n                    }, this)\n                  }, account.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Search, {\n                  placeholder: \"\\u641C\\u7D22...\",\n                  allowClear: true,\n                  onSearch: handleSearch,\n                  style: {\n                    width: 200\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 37\n                  }, this),\n                  onClick: handleRefresh,\n                  children: \"\\u5237\\u65B0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this),\n              style: {\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  color: '#666'\n                },\n                children: currentConfig.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table, {\n              columns: generateColumns(),\n              dataSource: dataList,\n              rowKey: \"id\",\n              loading: loading,\n              pagination: {\n                ...pagination,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`\n              },\n              onChange: handleTableChange,\n              scroll: {\n                x: 'max-content'\n              },\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), !currentConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Spin, {\n              size: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginTop: 16\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n};\n_s(DataDetails, \"QerZ/tKyYwlXWAjrrWC1twqnl84=\");\n_c = DataDetails;\nexport default DataDetails;\nvar _c;\n$RefreshReg$(_c, \"DataDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "<PERSON><PERSON>", "Table", "Card", "Select", "Input", "<PERSON><PERSON>", "message", "Spin", "Tag", "Space", "ReloadOutlined", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "Content", "Search", "Option", "DataDetails", "_s", "selectedPlatform", "setSelectedPlatform", "selectedDataType", "setSelectedDataType", "selectedAccount", "setSelectedAccount", "accounts", "setAccounts", "dataList", "setDataList", "dataConfig", "setDataConfig", "loading", "setLoading", "accountSummary", "setAccountSummary", "growthSummary", "setGrowthSummary", "overviewLoading", "setOverviewLoading", "pagination", "setPagination", "current", "pageSize", "total", "searchText", "setSearchText", "platformConfig", "wechat_mp", "name", "dataTypes", "content_trend", "content_source", "content_detail", "user_channel", "user_source", "wechat_channels", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menuItems", "key", "label", "children", "disabled", "fetchDataConfig", "fetchAccounts", "fetchOverviewData", "fetchDataList", "response", "get", "data", "success", "data_types", "error", "console", "detail", "params", "page", "page_size", "search", "undefined", "sort_field", "sort_order", "account_id", "prev", "handleMenuClick", "parts", "split", "length", "platform", "slice", "join", "dataType", "handleTableChange", "paginationInfo", "handleSearch", "value", "handleRefresh", "userSourceMapping", "generateColumns", "config", "columns", "map", "col", "title", "dataIndex", "render", "type", "Date", "toLocaleDateString", "toLocaleString", "href", "target", "rel", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "String", "sorter", "width", "fixed", "currentConfig", "style", "height", "theme", "borderRight", "padding", "borderBottom", "mode", "<PERSON><PERSON><PERSON><PERSON>", "items", "onClick", "background", "extra", "onChange", "placeholder", "color", "account", "id", "allowClear", "onSearch", "icon", "marginBottom", "margin", "description", "dataSource", "<PERSON><PERSON><PERSON>", "showSizeChanger", "showQuickJumper", "showTotal", "range", "scroll", "x", "size", "textAlign", "marginTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Table, Card, Select, Input, Button, message, Spin, Tag, Space } from 'antd';\nimport { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';\nimport api from '../services/api';\n\nconst { Sider, Content } = Layout;\nconst { Search } = Input;\nconst { Option } = Select;\n\ninterface Account {\n  id: number;\n  name: string;\n  login_status: boolean;\n  last_login_time: string | null;\n  created_at: string;\n}\n\ninterface DataItem {\n  id: number;\n  account_id: number;\n  [key: string]: any;\n}\n\ninterface DataConfig {\n  name: string;\n  description: string;\n  columns: Array<{\n    key: string;\n    title: string;\n    type: string;\n  }>;\n}\n\ninterface DataTypeConfig {\n  [key: string]: DataConfig;\n}\n\nconst DataDetails: React.FC = () => {\n  const [selectedPlatform, setSelectedPlatform] = useState<string>('wechat_mp');\n  const [selectedDataType, setSelectedDataType] = useState<string>('content_trend');\n  const [selectedAccount, setSelectedAccount] = useState<number | string | null>('all');\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [dataList, setDataList] = useState<DataItem[]>([]);\n  const [dataConfig, setDataConfig] = useState<DataTypeConfig>({});\n  const [loading, setLoading] = useState(false);\n  const [accountSummary, setAccountSummary] = useState<any>(null);\n  const [growthSummary, setGrowthSummary] = useState<any>(null);\n  const [overviewLoading, setOverviewLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 20,\n    total: 0,\n  });\n  const [searchText, setSearchText] = useState<string>('');\n\n  // 平台配置\n  const platformConfig = {\n    wechat_mp: {\n      name: '微信公众号',\n      dataTypes: {\n        content_trend: '内容数据趋势明细',\n        content_source: '内容流量来源明细',\n        content_detail: '内容已通知内容明细',\n        user_channel: '用户增长明细',\n        user_source: '用户来源明细'\n      }\n    },\n    wechat_channels: {\n      name: '视频号',\n      dataTypes: {}\n    },\n    xiaohongshu: {\n      name: '小红书',\n      dataTypes: {}\n    }\n  };\n\n  // 菜单项\n  const menuItems = [\n    {\n      key: 'wechat_mp',\n      label: '微信公众号',\n      children: [\n        { key: 'wechat_mp_overview', label: '总览' },\n        { key: 'wechat_mp_content_trend', label: '内容数据趋势明细' },\n        { key: 'wechat_mp_content_source', label: '内容流量来源明细' },\n        { key: 'wechat_mp_content_detail', label: '内容已通知内容明细' },\n        { key: 'wechat_mp_user_channel', label: '用户增长明细' },\n        { key: 'wechat_mp_user_source', label: '用户来源明细' }\n      ]\n    },\n    {\n      key: 'wechat_channels',\n      label: '视频号',\n      disabled: true,\n      children: [\n        { key: 'wechat_channels_placeholder', label: '敬请期待', disabled: true }\n      ]\n    },\n    {\n      key: 'xiaohongshu',\n      label: '小红书',\n      disabled: true,\n      children: [\n        { key: 'xiaohongshu_placeholder', label: '敬请期待', disabled: true }\n      ]\n    }\n  ];\n\n  useEffect(() => {\n    fetchDataConfig();\n    fetchAccounts();\n  }, []);\n\n  useEffect(() => {\n    if (selectedAccount && selectedDataType) {\n      if (selectedDataType === 'overview') {\n        fetchOverviewData();\n      } else {\n        fetchDataList();\n      }\n    }\n  }, [selectedAccount, selectedDataType, pagination.current, pagination.pageSize, searchText]);\n\n  const fetchDataConfig = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/config');\n      if (response.data.success) {\n        setDataConfig(response.data.data_types);\n      }\n    } catch (error: any) {\n      console.error('获取数据配置失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取数据配置失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取数据配置失败');\n      }\n    }\n  };\n\n  const fetchAccounts = async () => {\n    try {\n      const response = await api.get('/data-details/wechat-mp/accounts');\n      if (response.data.success) {\n        setAccounts(response.data.accounts);\n        // 默认选择\"全部\"\n        setSelectedAccount('all');\n      }\n    } catch (error: any) {\n      console.error('获取账号列表失败:', error);\n      if (error.response) {\n        console.error('错误响应:', error.response.data);\n        message.error(`获取账号列表失败: ${error.response.data.detail || error.message}`);\n      } else {\n        message.error('获取账号列表失败');\n      }\n    }\n  };\n\n  const fetchDataList = async () => {\n    if (!selectedAccount || !selectedDataType) return;\n\n    setLoading(true);\n    try {\n      const params: any = {\n        page: pagination.current,\n        page_size: pagination.pageSize,\n        search: searchText || undefined,\n        sort_field: 'created_at',\n        sort_order: 'desc'\n      };\n\n      // 只有当选择了具体账号时才传递 account_id 参数\n      if (selectedAccount !== 'all') {\n        params.account_id = selectedAccount;\n      }\n\n      const response = await api.get(`/data-details/wechat-mp/${selectedDataType}`, { params });\n      \n      if (response.data.success) {\n        setDataList(response.data.data);\n        setPagination(prev => ({\n          ...prev,\n          total: response.data.total,\n          current: response.data.page\n        }));\n      } else {\n        message.error(response.data.error || '获取数据失败');\n      }\n    } catch (error) {\n      message.error('获取数据列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    const parts = key.split('_');\n    if (parts.length >= 3) {\n      const platform = parts.slice(0, 2).join('_'); // wechat_mp\n      const dataType = parts.slice(2).join('_'); // content_trend\n      \n      setSelectedPlatform(platform);\n      setSelectedDataType(dataType);\n      setPagination(prev => ({ ...prev, current: 1 }));\n    }\n  };\n\n  const handleTableChange = (paginationInfo: any) => {\n    setPagination(prev => ({\n      ...prev,\n      current: paginationInfo.current,\n      pageSize: paginationInfo.pageSize\n    }));\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchText(value);\n    setPagination(prev => ({ ...prev, current: 1 }));\n  };\n\n  const handleRefresh = () => {\n    fetchDataList();\n  };\n\n  // 用户来源映射\n  const userSourceMapping: { [key: string]: string } = {\n    \"0\": \"其他合计\",\n    \"1\": \"公众号搜索\",\n    \"17\": \"名片分享\",\n    \"30\": \"扫描二维码\",\n    \"57\": \"文章内账号名称\",\n    \"100\": \"微信广告\",\n    \"161\": \"他人转载\",\n    \"149\": \"小程序关注\",\n    \"200\": \"视频号\",\n    \"201\": \"直播\"\n  };\n\n  // 生成表格列配置\n  const generateColumns = () => {\n    const config = dataConfig[selectedDataType];\n    if (!config) return [];\n\n    return config.columns.map(col => ({\n      title: col.title,\n      dataIndex: col.key,\n      key: col.key,\n      render: (value: any) => {\n        if (col.type === 'date') {\n          return value ? new Date(value).toLocaleDateString() : '-';\n        }\n        if (col.type === 'datetime') {\n          return value ? new Date(value).toLocaleString() : '-';\n        }\n        if (col.type === 'number') {\n          return typeof value === 'number' ? value.toLocaleString() : value || 0;\n        }\n        if (col.type === 'url') {\n          return value ? (\n            <a href={value} target=\"_blank\" rel=\"noopener noreferrer\">\n              查看链接\n            </a>\n          ) : '-';\n        }\n        if (col.type === 'user_source') {\n          return userSourceMapping[String(value)] || `未知来源(${value})`;\n        }\n        return value || '-';\n      },\n      sorter: col.type === 'number' || col.type === 'date' || col.type === 'datetime',\n      width: col.key === 'account_name' ? 150 :\n             col.key === 'updated_at' ? 180 :\n             col.type === 'text' ? 200 : 120,\n      fixed: col.key === 'account_name' ? 'left' as const :\n             col.key === 'updated_at' ? 'right' as const : undefined\n    }));\n  };\n\n  const currentConfig = dataConfig[selectedDataType];\n\n  return (\n    <div style={{ height: '100vh' }}>\n      <Layout style={{ height: '100%' }}>\n        <Sider width={250} theme=\"light\" style={{ borderRight: '1px solid #f0f0f0' }}>\n          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>\n            <h3>数据明细</h3>\n          </div>\n          <Menu\n            mode=\"inline\"\n            selectedKeys={[`${selectedPlatform}_${selectedDataType}`]}\n            items={menuItems}\n            onClick={handleMenuClick}\n            style={{ height: 'calc(100% - 64px)', borderRight: 0 }}\n          />\n        </Sider>\n        \n        <Layout>\n          <Content style={{ padding: '24px', background: '#fff' }}>\n            {currentConfig && (\n              <>\n                <Card \n                  title={currentConfig.name}\n                  extra={\n                    <Space>\n                      <Select\n                        value={selectedAccount}\n                        onChange={setSelectedAccount}\n                        style={{ width: 200 }}\n                        placeholder=\"选择账号\"\n                      >\n                        <Option key=\"all\" value=\"all\">\n                          <Space>\n                            全部账号\n                            <Tag color=\"blue\">\n                              {accounts.length} 个账号\n                            </Tag>\n                          </Space>\n                        </Option>\n                        {accounts.map(account => (\n                          <Option key={account.id} value={account.id}>\n                            <Space>\n                              {account.name}\n                            </Space>\n                          </Option>\n                        ))}\n                      </Select>\n                      <Search\n                        placeholder=\"搜索...\"\n                        allowClear\n                        onSearch={handleSearch}\n                        style={{ width: 200 }}\n                      />\n                      <Button icon={<ReloadOutlined />} onClick={handleRefresh}>\n                        刷新\n                      </Button>\n                    </Space>\n                  }\n                  style={{ marginBottom: 16 }}\n                >\n                  <p style={{ margin: 0, color: '#666' }}>{currentConfig.description}</p>\n                </Card>\n\n                <Table\n                  columns={generateColumns()}\n                  dataSource={dataList}\n                  rowKey=\"id\"\n                  loading={loading}\n                  pagination={{\n                    ...pagination,\n                    showSizeChanger: true,\n                    showQuickJumper: true,\n                    showTotal: (total, range) => \n                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`\n                  }}\n                  onChange={handleTableChange}\n                  scroll={{ x: 'max-content' }}\n                  size=\"small\"\n                />\n              </>\n            )}\n            \n            {!currentConfig && (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin size=\"large\" />\n                <p style={{ marginTop: 16 }}>加载中...</p>\n              </div>\n            )}\n          </Content>\n        </Layout>\n      </Layout>\n    </div>\n  );\n};\n\nexport default DataDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,MAAM;AAClG,SAAyBC,cAAc,QAA0B,mBAAmB;AACpF,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGlB,MAAM;AACjC,MAAM;EAAEmB;AAAO,CAAC,GAAGd,KAAK;AACxB,MAAM;EAAEe;AAAO,CAAC,GAAGhB,MAAM;AA8BzB,MAAMiB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAS,WAAW,CAAC;EAC7E,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAS,eAAe,CAAC;EACjF,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAyB,KAAK,CAAC;EACrF,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAa,EAAE,CAAC;EACxD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAiB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAM,IAAI,CAAC;EAC/D,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC;IAC3C+C,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAS,EAAE,CAAC;;EAExD;EACA,MAAMoD,cAAc,GAAG;IACrBC,SAAS,EAAE;MACTC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE;QACTC,aAAa,EAAE,UAAU;QACzBC,cAAc,EAAE,UAAU;QAC1BC,cAAc,EAAE,WAAW;QAC3BC,YAAY,EAAE,QAAQ;QACtBC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,eAAe,EAAE;MACfP,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,CAAC;IACd,CAAC;IACDO,WAAW,EAAE;MACXR,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC1C;MAAED,GAAG,EAAE,yBAAyB;MAAEC,KAAK,EAAE;IAAW,CAAC,EACrD;MAAED,GAAG,EAAE,0BAA0B;MAAEC,KAAK,EAAE;IAAW,CAAC,EACtD;MAAED,GAAG,EAAE,0BAA0B;MAAEC,KAAK,EAAE;IAAY,CAAC,EACvD;MAAED,GAAG,EAAE,wBAAwB;MAAEC,KAAK,EAAE;IAAS,CAAC,EAClD;MAAED,GAAG,EAAE,uBAAuB;MAAEC,KAAK,EAAE;IAAS,CAAC;EAErD,CAAC,EACD;IACED,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,KAAK;IACZE,QAAQ,EAAE,IAAI;IACdD,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,6BAA6B;MAAEC,KAAK,EAAE,MAAM;MAAEE,QAAQ,EAAE;IAAK,CAAC;EAEzE,CAAC,EACD;IACEH,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,KAAK;IACZE,QAAQ,EAAE,IAAI;IACdD,QAAQ,EAAE,CACR;MAAEF,GAAG,EAAE,yBAAyB;MAAEC,KAAK,EAAE,MAAM;MAAEE,QAAQ,EAAE;IAAK,CAAC;EAErE,CAAC,CACF;EAEDlE,SAAS,CAAC,MAAM;IACdmE,eAAe,CAAC,CAAC;IACjBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAENpE,SAAS,CAAC,MAAM;IACd,IAAI4B,eAAe,IAAIF,gBAAgB,EAAE;MACvC,IAAIA,gBAAgB,KAAK,UAAU,EAAE;QACnC2C,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM;QACLC,aAAa,CAAC,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAAC1C,eAAe,EAAEF,gBAAgB,EAAEkB,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,EAAEE,UAAU,CAAC,CAAC;EAE5F,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAM1D,GAAG,CAAC2D,GAAG,CAAC,gCAAgC,CAAC;MAChE,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBvC,aAAa,CAACoC,QAAQ,CAACE,IAAI,CAACE,UAAU,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIA,KAAK,CAACL,QAAQ,EAAE;QAClBM,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACL,QAAQ,CAACE,IAAI,CAAC;QAC3CjE,OAAO,CAACoE,KAAK,CAAC,aAAaA,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACK,MAAM,IAAIF,KAAK,CAACpE,OAAO,EAAE,CAAC;MAC3E,CAAC,MAAM;QACLA,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF;EACF,CAAC;EAED,MAAMR,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM1D,GAAG,CAAC2D,GAAG,CAAC,kCAAkC,CAAC;MAClE,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB3C,WAAW,CAACwC,QAAQ,CAACE,IAAI,CAAC3C,QAAQ,CAAC;QACnC;QACAD,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO+C,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIA,KAAK,CAACL,QAAQ,EAAE;QAClBM,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACL,QAAQ,CAACE,IAAI,CAAC;QAC3CjE,OAAO,CAACoE,KAAK,CAAC,aAAaA,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACK,MAAM,IAAIF,KAAK,CAACpE,OAAO,EAAE,CAAC;MAC3E,CAAC,MAAM;QACLA,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF;EACF,CAAC;EAED,MAAMN,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC1C,eAAe,IAAI,CAACF,gBAAgB,EAAE;IAE3CW,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM0C,MAAW,GAAG;QAClBC,IAAI,EAAEpC,UAAU,CAACE,OAAO;QACxBmC,SAAS,EAAErC,UAAU,CAACG,QAAQ;QAC9BmC,MAAM,EAAEjC,UAAU,IAAIkC,SAAS;QAC/BC,UAAU,EAAE,YAAY;QACxBC,UAAU,EAAE;MACd,CAAC;;MAED;MACA,IAAIzD,eAAe,KAAK,KAAK,EAAE;QAC7BmD,MAAM,CAACO,UAAU,GAAG1D,eAAe;MACrC;MAEA,MAAM2C,QAAQ,GAAG,MAAM1D,GAAG,CAAC2D,GAAG,CAAC,2BAA2B9C,gBAAgB,EAAE,EAAE;QAAEqD;MAAO,CAAC,CAAC;MAEzF,IAAIR,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBzC,WAAW,CAACsC,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;QAC/B5B,aAAa,CAAC0C,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPvC,KAAK,EAAEuB,QAAQ,CAACE,IAAI,CAACzB,KAAK;UAC1BF,OAAO,EAAEyB,QAAQ,CAACE,IAAI,CAACO;QACzB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLxE,OAAO,CAACoE,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACG,KAAK,IAAI,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmD,eAAe,GAAGA,CAAC;IAAEzB;EAAqB,CAAC,KAAK;IACpD,MAAM0B,KAAK,GAAG1B,GAAG,CAAC2B,KAAK,CAAC,GAAG,CAAC;IAC5B,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;MACrB,MAAMC,QAAQ,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9C,MAAMC,QAAQ,GAAGN,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE3CrE,mBAAmB,CAACmE,QAAQ,CAAC;MAC7BjE,mBAAmB,CAACoE,QAAQ,CAAC;MAC7BlD,aAAa,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzC,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMkD,iBAAiB,GAAIC,cAAmB,IAAK;IACjDpD,aAAa,CAAC0C,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPzC,OAAO,EAAEmD,cAAc,CAACnD,OAAO;MAC/BC,QAAQ,EAAEkD,cAAc,CAAClD;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMmD,YAAY,GAAIC,KAAa,IAAK;IACtCjD,aAAa,CAACiD,KAAK,CAAC;IACpBtD,aAAa,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzC,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMsD,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,aAAa,CAAC,CAAC;EACjB,CAAC;;EAED;EACA,MAAM+B,iBAA4C,GAAG;IACnD,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,OAAO;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,SAAS;IACf,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,MAAM,GAAGrE,UAAU,CAACR,gBAAgB,CAAC;IAC3C,IAAI,CAAC6E,MAAM,EAAE,OAAO,EAAE;IAEtB,OAAOA,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,GAAG,KAAK;MAChCC,KAAK,EAAED,GAAG,CAACC,KAAK;MAChBC,SAAS,EAAEF,GAAG,CAAC3C,GAAG;MAClBA,GAAG,EAAE2C,GAAG,CAAC3C,GAAG;MACZ8C,MAAM,EAAGV,KAAU,IAAK;QACtB,IAAIO,GAAG,CAACI,IAAI,KAAK,MAAM,EAAE;UACvB,OAAOX,KAAK,GAAG,IAAIY,IAAI,CAACZ,KAAK,CAAC,CAACa,kBAAkB,CAAC,CAAC,GAAG,GAAG;QAC3D;QACA,IAAIN,GAAG,CAACI,IAAI,KAAK,UAAU,EAAE;UAC3B,OAAOX,KAAK,GAAG,IAAIY,IAAI,CAACZ,KAAK,CAAC,CAACc,cAAc,CAAC,CAAC,GAAG,GAAG;QACvD;QACA,IAAIP,GAAG,CAACI,IAAI,KAAK,QAAQ,EAAE;UACzB,OAAO,OAAOX,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACc,cAAc,CAAC,CAAC,GAAGd,KAAK,IAAI,CAAC;QACxE;QACA,IAAIO,GAAG,CAACI,IAAI,KAAK,KAAK,EAAE;UACtB,OAAOX,KAAK,gBACVpF,OAAA;YAAGmG,IAAI,EAAEf,KAAM;YAACgB,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAAnD,QAAA,EAAC;UAE1D;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GACF,GAAG;QACT;QACA,IAAId,GAAG,CAACI,IAAI,KAAK,aAAa,EAAE;UAC9B,OAAOT,iBAAiB,CAACoB,MAAM,CAACtB,KAAK,CAAC,CAAC,IAAI,QAAQA,KAAK,GAAG;QAC7D;QACA,OAAOA,KAAK,IAAI,GAAG;MACrB,CAAC;MACDuB,MAAM,EAAEhB,GAAG,CAACI,IAAI,KAAK,QAAQ,IAAIJ,GAAG,CAACI,IAAI,KAAK,MAAM,IAAIJ,GAAG,CAACI,IAAI,KAAK,UAAU;MAC/Ea,KAAK,EAAEjB,GAAG,CAAC3C,GAAG,KAAK,cAAc,GAAG,GAAG,GAChC2C,GAAG,CAAC3C,GAAG,KAAK,YAAY,GAAG,GAAG,GAC9B2C,GAAG,CAACI,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;MACtCc,KAAK,EAAElB,GAAG,CAAC3C,GAAG,KAAK,cAAc,GAAG,MAAM,GACnC2C,GAAG,CAAC3C,GAAG,KAAK,YAAY,GAAG,OAAO,GAAYoB;IACvD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0C,aAAa,GAAG3F,UAAU,CAACR,gBAAgB,CAAC;EAElD,oBACEX,OAAA;IAAK+G,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAA9D,QAAA,eAC9BlD,OAAA,CAACd,MAAM;MAAC6H,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAA9D,QAAA,gBAChClD,OAAA,CAACG,KAAK;QAACyG,KAAK,EAAE,GAAI;QAACK,KAAK,EAAC,OAAO;QAACF,KAAK,EAAE;UAAEG,WAAW,EAAE;QAAoB,CAAE;QAAAhE,QAAA,gBAC3ElD,OAAA;UAAK+G,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAoB,CAAE;UAAAlE,QAAA,eACjElD,OAAA;YAAAkD,QAAA,EAAI;UAAI;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNzG,OAAA,CAACb,IAAI;UACHkI,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE,CAAC,GAAG7G,gBAAgB,IAAIE,gBAAgB,EAAE,CAAE;UAC1D4G,KAAK,EAAExE,SAAU;UACjByE,OAAO,EAAE/C,eAAgB;UACzBsC,KAAK,EAAE;YAAEC,MAAM,EAAE,mBAAmB;YAAEE,WAAW,EAAE;UAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAERzG,OAAA,CAACd,MAAM;QAAAgE,QAAA,eACLlD,OAAA,CAACI,OAAO;UAAC2G,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE;UAAO,CAAE;UAAAvE,QAAA,GACrD4D,aAAa,iBACZ9G,OAAA,CAAAE,SAAA;YAAAgD,QAAA,gBACElD,OAAA,CAACX,IAAI;cACHuG,KAAK,EAAEkB,aAAa,CAACxE,IAAK;cAC1BoF,KAAK,eACH1H,OAAA,CAACJ,KAAK;gBAAAsD,QAAA,gBACJlD,OAAA,CAACV,MAAM;kBACL8F,KAAK,EAAEvE,eAAgB;kBACvB8G,QAAQ,EAAE7G,kBAAmB;kBAC7BiG,KAAK,EAAE;oBAAEH,KAAK,EAAE;kBAAI,CAAE;kBACtBgB,WAAW,EAAC,0BAAM;kBAAA1E,QAAA,gBAElBlD,OAAA,CAACM,MAAM;oBAAW8E,KAAK,EAAC,KAAK;oBAAAlC,QAAA,eAC3BlD,OAAA,CAACJ,KAAK;sBAAAsD,QAAA,GAAC,0BAEL,eAAAlD,OAAA,CAACL,GAAG;wBAACkI,KAAK,EAAC,MAAM;wBAAA3E,QAAA,GACdnC,QAAQ,CAAC6D,MAAM,EAAC,qBACnB;sBAAA;wBAAA0B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GANE,KAAK;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOT,CAAC,EACR1F,QAAQ,CAAC2E,GAAG,CAACoC,OAAO,iBACnB9H,OAAA,CAACM,MAAM;oBAAkB8E,KAAK,EAAE0C,OAAO,CAACC,EAAG;oBAAA7E,QAAA,eACzClD,OAAA,CAACJ,KAAK;sBAAAsD,QAAA,EACH4E,OAAO,CAACxF;oBAAI;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC,GAHGqB,OAAO,CAACC,EAAE;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIf,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTzG,OAAA,CAACK,MAAM;kBACLuH,WAAW,EAAC,iBAAO;kBACnBI,UAAU;kBACVC,QAAQ,EAAE9C,YAAa;kBACvB4B,KAAK,EAAE;oBAAEH,KAAK,EAAE;kBAAI;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACFzG,OAAA,CAACR,MAAM;kBAAC0I,IAAI,eAAElI,OAAA,CAACH,cAAc;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAACe,OAAO,EAAEnC,aAAc;kBAAAnC,QAAA,EAAC;gBAE1D;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACR;cACDM,KAAK,EAAE;gBAAEoB,YAAY,EAAE;cAAG,CAAE;cAAAjF,QAAA,eAE5BlD,OAAA;gBAAG+G,KAAK,EAAE;kBAAEqB,MAAM,EAAE,CAAC;kBAAEP,KAAK,EAAE;gBAAO,CAAE;gBAAA3E,QAAA,EAAE4D,aAAa,CAACuB;cAAW;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEPzG,OAAA,CAACZ,KAAK;cACJqG,OAAO,EAAEF,eAAe,CAAC,CAAE;cAC3B+C,UAAU,EAAErH,QAAS;cACrBsH,MAAM,EAAC,IAAI;cACXlH,OAAO,EAAEA,OAAQ;cACjBQ,UAAU,EAAE;gBACV,GAAGA,UAAU;gBACb2G,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAEA,CAACzG,KAAK,EAAE0G,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ1G,KAAK;cAC1C,CAAE;cACF0F,QAAQ,EAAE1C,iBAAkB;cAC5B2D,MAAM,EAAE;gBAAEC,CAAC,EAAE;cAAc,CAAE;cAC7BC,IAAI,EAAC;YAAO;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA,eACF,CACH,EAEA,CAACK,aAAa,iBACb9G,OAAA;YAAK+G,KAAK,EAAE;cAAEgC,SAAS,EAAE,QAAQ;cAAE5B,OAAO,EAAE;YAAO,CAAE;YAAAjE,QAAA,gBACnDlD,OAAA,CAACN,IAAI;cAACoJ,IAAI,EAAC;YAAO;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrBzG,OAAA;cAAG+G,KAAK,EAAE;gBAAEiC,SAAS,EAAE;cAAG,CAAE;cAAA9F,QAAA,EAAC;YAAM;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACjG,EAAA,CAjVID,WAAqB;AAAA0I,EAAA,GAArB1I,WAAqB;AAmV3B,eAAeA,WAAW;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}