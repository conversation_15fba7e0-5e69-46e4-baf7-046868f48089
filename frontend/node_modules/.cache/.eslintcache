[{"/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx": "1", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx": "2", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx": "3", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx": "4", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx": "5", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx": "7", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx": "8", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts": "9", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx": "10", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts": "11", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx": "12", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataUpdate.tsx": "13"}, {"size": 272, "mtime": *************, "results": "14", "hashOfConfig": "15"}, {"size": 1393, "mtime": *************, "results": "16", "hashOfConfig": "17"}, {"size": 2691, "mtime": *************, "results": "18", "hashOfConfig": "17"}, {"size": 646, "mtime": *************, "results": "19", "hashOfConfig": "15"}, {"size": 1976, "mtime": *************, "results": "20", "hashOfConfig": "15"}, {"size": 5101, "mtime": *************, "results": "21", "hashOfConfig": "17"}, {"size": 29678, "mtime": 1753039684400, "results": "22", "hashOfConfig": "17"}, {"size": 2050, "mtime": 1752747944753, "results": "23", "hashOfConfig": "15"}, {"size": 763, "mtime": 1753039109233, "results": "24", "hashOfConfig": "17"}, {"size": 10389, "mtime": 1753033841869, "results": "25", "hashOfConfig": "17"}, {"size": 2912, "mtime": 1753033799016, "results": "26", "hashOfConfig": "17"}, {"size": 17184, "mtime": 1753079231555, "results": "27", "hashOfConfig": "17"}, {"size": 11405, "mtime": 1753087767599, "results": "28", "hashOfConfig": "17"}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sxxfvo", {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ix7n9v", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx", ["68"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx", ["69"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts", ["70"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/FeishuAppManage.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/feishuAppService.ts", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataDetails.tsx", ["71", "72", "73", "74"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/DataUpdate.tsx", ["75", "76"], [], {"ruleId": "77", "severity": 1, "message": "78", "line": 4, "column": 24, "nodeType": "79", "messageId": "80", "endLine": 4, "endColumn": 41}, {"ruleId": "77", "severity": 1, "message": "81", "line": 337, "column": 9, "nodeType": "79", "messageId": "80", "endLine": 337, "endColumn": 26}, {"ruleId": "77", "severity": 1, "message": "82", "line": 2, "column": 10, "nodeType": "79", "messageId": "80", "endLine": 2, "endColumn": 17}, {"ruleId": "77", "severity": 1, "message": "83", "line": 3, "column": 10, "nodeType": "79", "messageId": "80", "endLine": 3, "endColumn": 24}, {"ruleId": "77", "severity": 1, "message": "84", "line": 3, "column": 42, "nodeType": "79", "messageId": "80", "endLine": 3, "endColumn": 58}, {"ruleId": "77", "severity": 1, "message": "85", "line": 64, "column": 9, "nodeType": "79", "messageId": "80", "endLine": 64, "endColumn": 23}, {"ruleId": "86", "severity": 1, "message": "87", "line": 143, "column": 6, "nodeType": "88", "endLine": 143, "endColumn": 94, "suggestions": "89"}, {"ruleId": "77", "severity": 1, "message": "90", "line": 4, "column": 43, "nodeType": "79", "messageId": "80", "endLine": 4, "endColumn": 50}, {"ruleId": "86", "severity": 1, "message": "91", "line": 221, "column": 6, "nodeType": "88", "endLine": 221, "endColumn": 8, "suggestions": "92"}, "@typescript-eslint/no-unused-vars", "'DashboardOutlined' is defined but never used.", "Identifier", "unusedVar", "'handleForceLogout' is assigned a value but never used.", "'message' is defined but never used.", "'SearchOutlined' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'platformConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDataList'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["93"], "'Divider' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkRunningTask' and 'pollingInterval'. Either include them or remove the dependency array.", ["94"], {"desc": "95", "fix": "96"}, {"desc": "97", "fix": "98"}, "Update the dependencies array to be: [selectedAccount, selectedDataType, pagination.pageSize, searchText, fetchDataList]", {"range": "99", "text": "100"}, "Update the dependencies array to be: [checkRunningTask, pollingInterval]", {"range": "101", "text": "102"}, [3750, 3838], "[selectedAccount, selectedDataType, pagination.pageSize, searchText, fetchDataList]", [6052, 6054], "[checkRunningTask, pollingInterval]"]