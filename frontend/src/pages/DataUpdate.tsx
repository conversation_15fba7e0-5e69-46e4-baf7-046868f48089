import React, { useState, useEffect } from 'react';
import { 
  Card, DatePicker, Button, Progress, Alert, message, 
  Descriptions, Table, Space, Typography, Divider 
} from 'antd';
import { PlayCircleOutlined, ReloadOutlined, HistoryOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import api from '../services/api';

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

interface UpdateTask {
  task_id: number;
  start_date: string;
  end_date: string;
  status: string;
  total_accounts: number;
  completed_accounts: number;
  current_account_name?: string;
  current_step?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  progress_percent: number;
}

interface DataRange {
  min_date?: string;
  max_date?: string;
  total_records: number;
  last_update_time?: string;
  last_update_status?: string;
}

const DataUpdate: React.FC = () => {
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentTask, setCurrentTask] = useState<UpdateTask | null>(null);
  const [dataRange, setDataRange] = useState<DataRange | null>(null);
  const [history, setHistory] = useState<any[]>([]);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // 获取当前数据范围
  const fetchDataRange = async () => {
    try {
      const response = await api.get('/data-update/current-range');
      if (response.data.success) {
        setDataRange(response.data.overall);
      }
    } catch (error) {
      console.error('获取数据范围失败:', error);
    }
  };

  // 获取历史记录
  const fetchHistory = async () => {
    try {
      const response = await api.get('/data-update/history');
      if (response.data.success) {
        setHistory(response.data.data);
      }
    } catch (error) {
      console.error('获取历史记录失败:', error);
    }
  };

  // 检查是否有正在运行的任务
  const checkRunningTask = async () => {
    try {
      const response = await api.get('/data-update/running-task');
      if (response.data.success && response.data.has_running_task) {
        setCurrentTask(response.data.task);
        startPolling(response.data.task.task_id);
      }
    } catch (error) {
      console.error('检查运行任务失败:', error);
    }
  };

  // 开始轮询任务状态
  const startPolling = (taskId: number) => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    const interval = setInterval(async () => {
      try {
        const response = await api.get(`/data-update/status/${taskId}`);
        if (response.data.success) {
          setCurrentTask(response.data);
          
          // 如果任务完成，停止轮询
          if (response.data.status === 'completed' || response.data.status === 'failed') {
            clearInterval(interval);
            setPollingInterval(null);
            fetchHistory(); // 刷新历史记录
            fetchDataRange(); // 刷新数据范围
            
            if (response.data.status === 'completed') {
              message.success('数据更新完成！');
            } else {
              message.error('数据更新失败！');
            }
          }
        }
      } catch (error) {
        console.error('获取任务状态失败:', error);
        clearInterval(interval);
        setPollingInterval(null);
      }
    }, 2000);

    setPollingInterval(interval);
  };

  // 启动数据更新
  const handleStartUpdate = async () => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) {
      message.error('请选择日期范围');
      return;
    }

    const startDate = dateRange[0].format('YYYY-MM-DD');
    const endDate = dateRange[1].format('YYYY-MM-DD');

    // 验证日期范围
    const daysDiff = dateRange[1].diff(dateRange[0], 'day');
    if (daysDiff > 30) {
      message.error('日期范围不能超过30天');
      return;
    }

    setLoading(true);
    try {
      const response = await api.post('/data-update/start', {
        start_date: startDate,
        end_date: endDate
      });

      if (response.data.success) {
        message.success('数据更新任务已启动');
        setCurrentTask({
          task_id: response.data.task_id,
          start_date: startDate,
          end_date: endDate,
          status: 'running',
          total_accounts: response.data.total_accounts,
          completed_accounts: 0,
          current_step: '准备中...',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          progress_percent: 0
        });
        
        startPolling(response.data.task_id);
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '启动更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 历史记录表格列
  const historyColumns = [
    {
      title: '日期范围',
      key: 'date_range',
      render: (record: any) => `${record.start_date} 至 ${record.end_date}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          'running': { text: '运行中', color: 'blue' },
          'completed': { text: '已完成', color: 'green' },
          'failed': { text: '失败', color: 'red' }
        };
        const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
        return <Text style={{ color: config.color }}>{config.text}</Text>;
      }
    },
    {
      title: '账号数',
      key: 'accounts',
      render: (record: any) => `${record.completed_accounts}/${record.total_accounts}`
    },
    {
      title: '进度',
      dataIndex: 'progress_percent',
      key: 'progress_percent',
      render: (percent: number) => `${percent}%`
    },
    {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration'
    },
    {
      title: '开始时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString()
    }
  ];

  useEffect(() => {
    fetchDataRange();
    fetchHistory();
    checkRunningTask();

    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, []);

  const isTaskRunning = Boolean(currentTask && currentTask.status === 'running');

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据更新</Title>
      
      {/* 当前数据范围 */}
      <Card title="当前数据范围" style={{ marginBottom: 24 }}>
        {dataRange ? (
          <Descriptions column={3}>
            <Descriptions.Item label="开始日期">
              {dataRange.min_date || '无数据'}
            </Descriptions.Item>
            <Descriptions.Item label="结束日期">
              {dataRange.max_date || '无数据'}
            </Descriptions.Item>
            <Descriptions.Item label="总记录数">
              {dataRange.total_records?.toLocaleString() || 0}
            </Descriptions.Item>
            <Descriptions.Item label="最后更新时间">
              {dataRange.last_update_time ? new Date(dataRange.last_update_time).toLocaleString() : '无'}
            </Descriptions.Item>
            <Descriptions.Item label="更新状态">
              {dataRange.last_update_status === 'completed' ? '成功' :
               dataRange.last_update_status === 'failed' ? '失败' : '无'}
            </Descriptions.Item>
          </Descriptions>
        ) : (
          <Text>加载中...</Text>
        )}
      </Card>

      {/* 数据更新操作 */}
      <Card title="数据更新操作" style={{ marginBottom: 24 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>选择日期范围（最长30天）：</Text>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              disabled={isTaskRunning}
              style={{ marginLeft: 16 }}
              disabledDate={(current) => {
                // 禁用未来日期
                return current && current > dayjs().endOf('day');
              }}
            />
          </div>
          
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={handleStartUpdate}
            loading={loading}
            disabled={isTaskRunning}
            size="large"
          >
            开始更新
          </Button>

          {isTaskRunning && (
            <Alert
              message="注意"
              description="数据更新过程中会先清空现有数据，然后重新下载所有账号的数据。请耐心等待。"
              type="warning"
              showIcon
            />
          )}
        </Space>
      </Card>

      {/* 当前任务进度 */}
      {currentTask && (
        <Card title="当前任务进度" style={{ marginBottom: 24 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Progress 
              percent={currentTask.progress_percent} 
              status={currentTask.status === 'failed' ? 'exception' : 'active'}
              strokeColor={currentTask.status === 'completed' ? '#52c41a' : undefined}
            />
            
            <Descriptions column={2}>
              <Descriptions.Item label="日期范围">
                {currentTask.start_date} 至 {currentTask.end_date}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {currentTask.status === 'running' ? '运行中' : 
                 currentTask.status === 'completed' ? '已完成' : '失败'}
              </Descriptions.Item>
              <Descriptions.Item label="当前账号">
                {currentTask.current_account_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="当前步骤">
                {currentTask.current_step || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="进度">
                {currentTask.completed_accounts}/{currentTask.total_accounts} 个账号
              </Descriptions.Item>
              <Descriptions.Item label="开始时间">
                {new Date(currentTask.created_at).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>

            {currentTask.error_message && (
              <Alert
                message="错误信息"
                description={currentTask.error_message}
                type="error"
                showIcon
              />
            )}
          </Space>
        </Card>
      )}

      {/* 历史记录 */}
      <Card 
        title={
          <Space>
            <HistoryOutlined />
            历史更新记录
          </Space>
        }
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={() => { fetchHistory(); fetchDataRange(); }}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={historyColumns}
          dataSource={history}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default DataUpdate;
