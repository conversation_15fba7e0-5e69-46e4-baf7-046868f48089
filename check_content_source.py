#!/usr/bin/env python3
"""
检查内容流量来源明细表的数据
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.data_details_service import DataDetailsService

def check_content_source_data():
    """检查内容流量来源明细表的数据"""
    
    # 查找最新的content_source文件
    import glob
    pattern = 'data/feishu_app_4/account_*/wechat_data_account_*_content_source_*.xlsx'
    files = glob.glob(pattern)
    
    if not files:
        print("未找到content_source文件")
        return
    
    # 使用最新的文件
    latest_file = max(files, key=os.path.getctime)
    print(f"检查文件: {latest_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(latest_file)
        
        print(f"文件包含 {len(df)} 行数据")
        print(f"列名: {df.columns.tolist()}")
        
        # 检查发表日期列
        if '发表日期' in df.columns:
            print(f"\n'发表日期'列分析:")
            date_col = df['发表日期']
            print(f"数据类型: {date_col.dtype}")
            print(f"非空值数量: {date_col.notna().sum()}")
            print(f"空值数量: {date_col.isna().sum()}")
            
            print("\n前10个发表日期值:")
            for i, val in enumerate(date_col.head(10)):
                parsed = DataDetailsService._parse_date(val)
                print(f"  {i+1}: {repr(val)} (type: {type(val).__name__}) -> 解析结果: {parsed}")
        else:
            print("\n未找到'发表日期'列")
            
        print("\n前3行完整数据:")
        print(df.head(3))
            
    except Exception as e:
        print(f"读取文件失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_content_source_data()
