from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from app.routers import auth, accounts, wechat, analytics, feishu, feishu_app, data_details
from app.database import engine, Base

# 加载环境变量
load_dotenv()

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(title="社交媒体数据管理系统", version="1.0.0")

# CORS配置
import os
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由注册
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(accounts.router, prefix="/api/accounts", tags=["账号管理"])
app.include_router(wechat.router, prefix="/api/wechat", tags=["微信公众号"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["数据分析"])
app.include_router(data_details.router, prefix="/api/data-details", tags=["数据明细"])
app.include_router(feishu.router, tags=["飞书多维表格"])
app.include_router(feishu_app.router, tags=["飞书应用管理"])

@app.get("/")
async def root():
    return {"message": "社交媒体数据管理系统"}