#!/usr/bin/env python3
"""
测试日期解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.data_details_service import DataDetailsService

def test_date_parsing():
    """测试日期解析功能"""
    
    test_values = [
        2025071811,  # 10位数字格式
        20250718,    # 8位数字格式
        "2025071811", # 10位字符串格式
        "20250718",   # 8位字符串格式
        "2025-07-18", # 标准日期格式
        "2025/07/18", # 斜杠格式
        None,         # 空值
        "",           # 空字符串
    ]
    
    print("测试日期解析功能:")
    print("=" * 50)
    
    for i, value in enumerate(test_values, 1):
        result = DataDetailsService._parse_date(value)
        print(f"{i}. 输入: {repr(value)} (type: {type(value).__name__}) -> 解析结果: {result}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    test_date_parsing()
