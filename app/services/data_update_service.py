import asyncio
import uuid
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from app.models import (
    DataUpdateRecord, PlatformAccount,
    WeChatMPContentTrend, WeChatMPContentSource, 
    WeChatMPContentDetail, WeChatMPUserChannel, WeChatMPUserSource
)
from app.services.wechat_service import WeChatMPService
from app.database import SessionLocal
import logging

logger = logging.getLogger(__name__)


class DataUpdateService:
    """数据更新服务类"""
    
    # 需要清空的数据表
    DATA_TABLES = [
        WeChatMPContentTrend,
        WeChatMPContentSource, 
        WeChatMPContentDetail,
        WeChatMPUserChannel,
        WeChatMPUserSource
    ]
    
    # 数据类型映射
    DATA_TYPES = ['content_trend', 'content_source', 'content_detail', 'user_channel']
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> Dict[str, Any]:
        """验证日期范围
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            验证结果
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            if start > end:
                return {"success": False, "error": "开始日期不能晚于结束日期"}
            
            if (end - start).days > 30:
                return {"success": False, "error": "日期范围不能超过30天"}
            
            return {"success": True, "start_date": start, "end_date": end}
            
        except ValueError as e:
            return {"success": False, "error": f"日期格式错误: {str(e)}"}
    
    @staticmethod
    def clear_data_tables(db: Session) -> Dict[str, Any]:
        """清空数据表
        
        Args:
            db: 数据库会话
            
        Returns:
            清空结果
        """
        try:
            cleared_counts = {}
            
            # 按依赖关系顺序清空表
            for table_class in DataUpdateService.DATA_TABLES:
                count = db.query(table_class).count()
                db.query(table_class).delete()
                cleared_counts[table_class.__tablename__] = count
                
            db.commit()
            
            return {
                "success": True, 
                "cleared_counts": cleared_counts,
                "total_cleared": sum(cleared_counts.values())
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"清空数据表失败: {str(e)}")
            return {"success": False, "error": f"清空数据表失败: {str(e)}"}
    
    @staticmethod
    def get_logged_accounts(db: Session) -> List[PlatformAccount]:
        """获取所有已登录的微信公众号账号
        
        Args:
            db: 数据库会话
            
        Returns:
            已登录的账号列表
        """
        return db.query(PlatformAccount).filter(
            and_(
                PlatformAccount.platform.in_(["wechat_mp", "wechat_service"]),
                PlatformAccount.login_status == True
            )
        ).all()
    
    @staticmethod
    def create_update_record(db: Session, start_date: date, end_date: date, total_accounts: int) -> DataUpdateRecord:
        """创建更新记录
        
        Args:
            db: 数据库会话
            start_date: 开始日期
            end_date: 结束日期
            total_accounts: 总账号数
            
        Returns:
            更新记录对象
        """
        record = DataUpdateRecord(
            start_date=start_date,
            end_date=end_date,
            status='running',
            total_accounts=total_accounts,
            completed_accounts=0
        )
        db.add(record)
        db.commit()
        db.refresh(record)
        return record
    
    @staticmethod
    def update_record_status(db: Session, record_id: int, **kwargs) -> bool:
        """更新记录状态
        
        Args:
            db: 数据库会话
            record_id: 记录ID
            **kwargs: 要更新的字段
            
        Returns:
            更新是否成功
        """
        try:
            record = db.query(DataUpdateRecord).filter(DataUpdateRecord.id == record_id).first()
            if not record:
                return False
                
            for key, value in kwargs.items():
                if hasattr(record, key):
                    setattr(record, key, value)
            
            record.updated_at = datetime.utcnow()
            
            if kwargs.get('status') == 'completed':
                record.completed_at = datetime.utcnow()
                
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"更新记录状态失败: {str(e)}")
            db.rollback()
            return False
    
    @staticmethod
    async def process_account_data(account_id: int, start_date: str, end_date: str,
                                 record_id: int) -> Dict[str, Any]:
        """处理单个账号的数据更新

        Args:
            account_id: 账号ID
            start_date: 开始日期
            end_date: 结束日期
            record_id: 更新记录ID

        Returns:
            处理结果
        """
        db = SessionLocal()
        try:
            # 在当前会话中查询账号对象
            account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
            if not account:
                return {"success": False, "error": f"账号ID {account_id} 不存在"}

            # 更新状态：开始处理账号
            DataUpdateService.update_record_status(
                db, record_id,
                current_account_name=account.name,
                current_step="开始批量下载数据"
            )

            # 创建微信服务实例
            wechat_service = WeChatMPService(account_id=account_id)

            # 尝试加载已保存的登录状态
            if not await wechat_service.load_login_state():
                error_msg = f"账号 {account.name} 登录状态恢复失败，请重新登录"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # 批量下载各种数据类型
            try:
                DataUpdateService.update_record_status(
                    db, record_id,
                    current_step="批量下载微信数据"
                )

                # 使用批量下载方法
                download_result = await wechat_service.batch_download_data_excel(
                    start_date=start_date,
                    end_date=end_date,
                    data_types=DataUpdateService.DATA_TYPES
                )

                if not download_result or not download_result.get("success"):
                    error_msg = f"账号 {account.name} 批量下载失败: {download_result.get('message', '未知错误') if download_result else '无响应'}"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                logger.info(f"账号 {account.name} 批量下载成功: {download_result.get('message')}")

            except Exception as e:
                error_msg = f"账号 {account.name} 批量下载时发生错误: {str(e)}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # 同步到飞书
            if account.feishu_app_id:
                try:
                    DataUpdateService.update_record_status(
                        db, record_id,
                        current_step="同步到飞书"
                    )

                    from app.services.feishu_service import FeishuService
                    
                    sync_result = FeishuService.sync_wechat_data_to_feishu_core(account_id, db)

                    if sync_result.get("success"):
                        logger.info(f"账号 {account.name} 飞书同步成功: {sync_result.get('message')}")
                    else:
                        logger.warning(f"账号 {account.name} 飞书同步失败: {sync_result.get('message', '未知错误')}")
                        # 飞书同步失败不影响整体流程，只记录警告

                except Exception as e:
                    logger.warning(f"账号 {account.name} 飞书同步失败: {str(e)}")
                    # 飞书同步失败不影响整体流程，只记录警告

            return {"success": True, "account_name": account.name}
            
        except Exception as e:
            error_msg = f"处理账号时发生错误: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        finally:
            db.close()
    
    @staticmethod
    def get_current_data_range(db: Session) -> Dict[str, Any]:
        """获取当前数据范围（基于最新的更新记录）

        Args:
            db: 数据库会话

        Returns:
            数据范围信息
        """
        try:
            # 获取最新的已完成更新记录
            latest_record = db.query(DataUpdateRecord).filter(
                DataUpdateRecord.status.in_(['completed', 'failed'])
            ).order_by(DataUpdateRecord.created_at.desc()).first()

            if latest_record:
                # 计算总记录数
                total_records = 0
                for table_class in DataUpdateService.DATA_TABLES:
                    count = db.query(table_class).count()
                    total_records += count

                return {
                    "success": True,
                    "min_date": latest_record.start_date.isoformat(),
                    "max_date": latest_record.end_date.isoformat(),
                    "total_records": total_records,
                    "last_update_time": latest_record.completed_at.isoformat() if latest_record.completed_at else latest_record.updated_at.isoformat(),
                    "last_update_status": latest_record.status
                }
            else:
                # 没有更新记录，返回空数据范围
                return {
                    "success": True,
                    "min_date": None,
                    "max_date": None,
                    "total_records": 0,
                    "last_update_time": None,
                    "last_update_status": None
                }

        except Exception as e:
            logger.error(f"获取数据范围失败: {str(e)}")
            return {"success": False, "error": f"获取数据范围失败: {str(e)}"}

    @staticmethod
    async def start_data_update(start_date: str, end_date: str) -> Dict[str, Any]:
        """启动数据更新任务

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            任务启动结果，包含任务ID
        """
        # 验证日期范围
        date_validation = DataUpdateService.validate_date_range(start_date, end_date)
        if not date_validation["success"]:
            return date_validation

        db = SessionLocal()
        try:
            # 检查是否有正在运行的任务
            running_task = db.query(DataUpdateRecord).filter(
                DataUpdateRecord.status == 'running'
            ).first()

            if running_task:
                return {
                    "success": False,
                    "error": "已有数据更新任务正在运行，请等待完成后再试"
                }

            # 获取已登录账号
            accounts = DataUpdateService.get_logged_accounts(db)
            if not accounts:
                return {
                    "success": False,
                    "error": "没有找到已登录的微信公众号账号"
                }

            # 创建更新记录
            record = DataUpdateService.create_update_record(
                db,
                date_validation["start_date"],
                date_validation["end_date"],
                len(accounts)
            )

            # 启动异步任务
            # 传递账号ID列表而不是账号对象，避免跨会话问题
            account_ids = [account.id for account in accounts]
            asyncio.create_task(
                DataUpdateService._execute_update_task(
                    record.id, start_date, end_date, account_ids
                )
            )

            return {
                "success": True,
                "task_id": record.id,
                "total_accounts": len(accounts),
                "message": "数据更新任务已启动"
            }

        except Exception as e:
            logger.error(f"启动数据更新任务失败: {str(e)}")
            return {"success": False, "error": f"启动任务失败: {str(e)}"}
        finally:
            db.close()

    @staticmethod
    async def _execute_update_task(record_id: int, start_date: str, end_date: str,
                                 account_ids: List[int]):
        """执行数据更新任务（内部方法）

        Args:
            record_id: 更新记录ID
            start_date: 开始日期
            end_date: 结束日期
            account_ids: 账号ID列表
        """
        db = SessionLocal()
        try:
            logger.info(f"开始执行数据更新任务 {record_id}")

            # 重新查询账号对象，确保在当前会话中
            accounts = db.query(PlatformAccount).filter(
                PlatformAccount.id.in_(account_ids)
            ).all()

            # 1. 清空数据表
            DataUpdateService.update_record_status(
                db, record_id, current_step="清空现有数据"
            )

            clear_result = DataUpdateService.clear_data_tables(db)
            if not clear_result["success"]:
                DataUpdateService.update_record_status(
                    db, record_id,
                    status='failed',
                    error_message=f"清空数据失败: {clear_result['error']}"
                )
                return

            logger.info(f"已清空 {clear_result['total_cleared']} 条数据")

            # 2. 逐个处理账号
            completed_count = 0
            failed_accounts = []

            for i, account in enumerate(accounts, 1):
                logger.info(f"处理账号 {i}/{len(accounts)}: {account.name}")

                # 处理账号数据（注意会话隔离）
                result = await DataUpdateService.process_account_data(
                    account.id, start_date, end_date, record_id
                )

                if result["success"]:
                    completed_count += 1
                    logger.info(f"账号 {account.name} 处理成功")
                else:
                    failed_accounts.append({
                        "name": account.name,
                        "error": result.get("error", "未知错误")
                    })
                    logger.error(f"账号 {account.name} 处理失败: {result.get('error')}")

                # 更新进度
                DataUpdateService.update_record_status(
                    db, record_id, completed_accounts=completed_count
                )

                # 账号间添加延迟，避免请求过于频繁
                if i < len(accounts):
                    await asyncio.sleep(2)

            # 3. 完成任务
            if failed_accounts:
                failed_info = ', '.join([f"{acc['name']}({acc['error']})" for acc in failed_accounts])
                error_summary = f"部分账号处理失败: {failed_info}"
                DataUpdateService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="完成（部分失败）",
                    error_message=error_summary
                )
                logger.warning(f"任务 {record_id} 完成，但有 {len(failed_accounts)} 个账号失败")
            else:
                DataUpdateService.update_record_status(
                    db, record_id,
                    status='completed',
                    current_step="全部完成"
                )
                logger.info(f"任务 {record_id} 全部完成")

        except Exception as e:
            logger.error(f"执行更新任务 {record_id} 时发生错误: {str(e)}")
            DataUpdateService.update_record_status(
                db, record_id,
                status='failed',
                error_message=f"任务执行失败: {str(e)}"
            )
        finally:
            db.close()

    @staticmethod
    def get_update_status(db: Session, task_id: int) -> Dict[str, Any]:
        """获取更新任务状态

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            任务状态信息
        """
        try:
            record = db.query(DataUpdateRecord).filter(DataUpdateRecord.id == task_id).first()
            if not record:
                return {"success": False, "error": "任务不存在"}

            return {
                "success": True,
                "task_id": record.id,
                "start_date": record.start_date.isoformat(),
                "end_date": record.end_date.isoformat(),
                "status": record.status,
                "total_accounts": record.total_accounts,
                "completed_accounts": record.completed_accounts,
                "current_account_name": record.current_account_name,
                "current_step": record.current_step,
                "error_message": record.error_message,
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
                "completed_at": record.completed_at.isoformat() if record.completed_at else None,
                "progress_percent": round((record.completed_accounts / record.total_accounts) * 100, 1) if record.total_accounts > 0 else 0
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {"success": False, "error": f"获取任务状态失败: {str(e)}"}
