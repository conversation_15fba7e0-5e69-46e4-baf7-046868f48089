import pandas as pd
from typing import List, Dict, Any, Optional, Tuple, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from datetime import datetime, date
import io
from app.models import (
    WeChatMPContentTrend, WeChatMPContentSource,
    WeChatMPContentDetail, WeChatMPUserChannel,
    WeChatMPUserSource, PlatformAccount
)
from app.services.wechat_service import WeChatMPService


class DataDetailsService:
    """数据明细服务类"""
    
    # 数据类型到模型的映射
    MODEL_MAPPING = {
        'content_trend': WeChatMPContentTrend,
        'content_source': WeChatMPContentSource,
        'content_detail': WeChatMPContentDetail,
        'user_channel': WeChatMPUserChannel,
        'user_source': WeChatMPUserSource
    }
    
    @staticmethod
    def import_excel_data(
        db: Session, 
        account_id: int, 
        data_type: str, 
        excel_content: bytes
    ) -> Dict[str, Any]:
        """从Excel内容导入数据到数据库
        
        Args:
            db: 数据库会话
            account_id: 账号ID
            data_type: 数据类型
            excel_content: Excel文件内容
            
        Returns:
            导入结果统计
        """
        try:
            # 获取对应的模型类
            model_class = DataDetailsService.MODEL_MAPPING.get(data_type)
            if not model_class:
                return {"success": False, "error": f"不支持的数据类型: {data_type}"}
            
            # 获取DOWNLOAD_TEMPLATES配置
            wechat_service = WeChatMPService()
            config = wechat_service._get_download_config(data_type=data_type)
            if not config:
                return {"success": False, "error": f"找不到数据类型配置: {data_type}"}
            
            # 检查文件内容是否为HTML格式（特别是用户增长表）
            content_str = excel_content.decode('utf-8', errors='ignore')
            if content_str.strip().startswith('<html') and data_type == 'user_channel':
                # 解析HTML表格
                df = DataDetailsService._parse_html_table(content_str)
                # HTML表格通常已经是干净的数据，不需要跳过太多行
                data_start_row = 0  # HTML表格解析后直接使用
                fields = config.get('fields', [])
                data_df = df.copy()
            else:
                # 读取Excel文件
                df = pd.read_excel(io.BytesIO(excel_content))
                # 获取数据起始行和字段配置
                data_start_row = config.get('data_start_row', 2) - 1  # 转换为0基索引
                fields = config.get('fields', [])
                # 跳过标题行，获取数据行
                data_df = df.iloc[data_start_row:].copy()
            
            if data_df.empty:
                return {"success": False, "error": "Excel文件中没有数据"}
            
            # 重置列名为字段名
            if len(data_df.columns) >= len(fields):
                field_names = [field[0] for field in fields]
                data_df.columns = field_names + list(data_df.columns[len(fields):])
            
            # 根据数据类型处理数据
            imported_count = 0
            updated_count = 0
            
            if data_type == 'content_trend':
                imported_count, updated_count = DataDetailsService._import_content_trend(
                    db, account_id, data_df
                )
            elif data_type == 'content_source':
                imported_count, updated_count = DataDetailsService._import_content_source(
                    db, account_id, data_df
                )
            elif data_type == 'content_detail':
                imported_count, updated_count = DataDetailsService._import_content_detail(
                    db, account_id, data_df
                )
            elif data_type == 'user_channel':
                imported_count, updated_count = DataDetailsService._import_user_channel(
                    db, account_id, data_df
                )
            
            db.commit()
            
            return {
                "success": True,
                "imported_count": imported_count,
                "updated_count": updated_count,
                "total_processed": imported_count + updated_count
            }
            
        except Exception as e:
            db.rollback()
            return {"success": False, "error": f"导入失败: {str(e)}"}
    
    @staticmethod
    def _parse_date(date_value) -> Optional[date]:
        """解析日期值"""
        if pd.isna(date_value):
            return None

        # 处理整数类型（如 ******** 或 **********）
        if isinstance(date_value, (int, float)):
            date_str = str(int(date_value))
            # 如果是10位数字格式（如**********），只取前8位作为日期
            if len(date_str) >= 8:
                date_str = date_str[:8]
        elif isinstance(date_value, str):
            date_str = date_value.strip()
            # 如果是字符串且长度>=8，只取前8位作为日期
            if len(date_str) >= 8 and date_str.isdigit():
                date_str = date_str[:8]
        elif isinstance(date_value, datetime):
            return date_value.date()
        elif isinstance(date_value, date):
            return date_value
        else:
            return None

        # 尝试解析不同格式的日期字符串
        for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d']:
            try:
                return datetime.strptime(date_str, fmt).date()
            except ValueError:
                continue

        return None

    @staticmethod
    def _parse_datetime(datetime_value) -> Optional[datetime]:
        """解析日期时间值"""
        if pd.isna(datetime_value):
            return None

        # 处理整数类型（如 20250718）
        if isinstance(datetime_value, (int, float)):
            datetime_str = str(int(datetime_value))
        elif isinstance(datetime_value, str):
            datetime_str = datetime_value.strip()
        elif isinstance(datetime_value, datetime):
            return datetime_value
        elif isinstance(datetime_value, date):
            # 将date转换为datetime，时间设为00:00:00
            return datetime.combine(datetime_value, datetime.min.time())
        else:
            return None

        # 尝试解析不同格式的日期时间字符串
        formats = [
            '%Y-%m-%d %H:%M:%S',  # 2025-07-18 10:30:00
            '%Y/%m/%d %H:%M:%S',  # 2025/07/18 10:30:00
            '%Y%m%d %H:%M:%S',    # 20250718 10:30:00
            '%Y-%m-%d',           # 2025-07-18 (只有日期，时间设为00:00:00)
            '%Y/%m/%d',           # 2025/07/18
            '%Y%m%d'              # 20250718 (8位数字格式)
        ]

        for fmt in formats:
            try:
                return datetime.strptime(datetime_str, fmt)
            except ValueError:
                continue

        return None

    @staticmethod
    def _parse_html_table(html_content: str) -> pd.DataFrame:
        """解析HTML表格内容为DataFrame，专门处理用户增长表"""
        try:
            from io import StringIO
            import re

            # 直接从HTML中提取表格数据
            # 查找所有的表格行
            tr_pattern = r'<tr[^>]*>(.*?)</tr>'
            tr_matches = re.findall(tr_pattern, html_content, re.DOTALL)

            data_rows = []

            for tr_content in tr_matches:
                # 提取th和td中的数据
                cell_pattern = r'<(?:th|td)[^>]*>(.*?)</(?:th|td)>'
                cells = re.findall(cell_pattern, tr_content, re.DOTALL)

                if len(cells) >= 5:  # 用户增长表有5列
                    # 清理HTML标签和空白字符
                    clean_cells = []
                    for cell in cells:
                        clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                        clean_cells.append(clean_cell)

                    # 检查是否是数据行（第一个单元格是日期格式）
                    if len(clean_cells) >= 5:
                        first_cell = clean_cells[0]
                        if len(first_cell) == 10 and first_cell.count('-') == 2:
                            # 这是数据行，转换数值
                            row_data = [first_cell]  # 时间
                            for i in range(1, 5):  # 4个数值字段
                                try:
                                    value = int(clean_cells[i])
                                    row_data.append(value)
                                except (ValueError, TypeError):
                                    row_data.append(0)

                            if len(row_data) == 5:
                                data_rows.append(row_data)

            # 创建DataFrame
            if data_rows:
                df = pd.DataFrame(data_rows, columns=['时间', '新关注人数', '取消关注人数', '净增关注人数', '累积关注人数'])
                print(f"HTML表格处理成功，转换为 {df.shape} 的DataFrame")
                return df
            else:
                print("未找到有效的数据行")
                return pd.DataFrame()

        except Exception as e:
            print(f"HTML表格解析失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    @staticmethod
    def _parse_int(value) -> int:
        """解析整数值"""
        if pd.isna(value):
            return 0
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return 0
    
    @staticmethod
    def _parse_str(value) -> str:
        """解析字符串值"""
        if pd.isna(value):
            return ""
        return str(value).strip()
    
    @staticmethod
    def _import_content_trend(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入内容数据趋势明细"""
        imported_count = 0
        updated_count = 0
        
        for _, row in df.iterrows():
            date_value = DataDetailsService._parse_date(row.get('日期'))
            if not date_value:
                continue
            
            # 检查是否已存在
            existing = db.query(WeChatMPContentTrend).filter(
                and_(
                    WeChatMPContentTrend.account_id == account_id,
                    WeChatMPContentTrend.date == date_value
                )
            ).first()
            
            data = {
                'account_id': account_id,
                'date': date_value,
                'read_count': DataDetailsService._parse_int(row.get('阅读次数')),
                'read_user_count': DataDetailsService._parse_int(row.get('阅读人数')),
                'share_count': DataDetailsService._parse_int(row.get('分享次数')),
                'share_user_count': DataDetailsService._parse_int(row.get('分享人数')),
                'read_original_count': DataDetailsService._parse_int(row.get('阅读原文次数')),
                'read_original_user_count': DataDetailsService._parse_int(row.get('阅读原文人数')),
                'collect_count': DataDetailsService._parse_int(row.get('收藏次数')),
                'collect_user_count': DataDetailsService._parse_int(row.get('收藏人数')),
                'publish_count': DataDetailsService._parse_int(row.get('群发篇数')),
                'channel': DataDetailsService._parse_str(row.get('渠道'))
            }
            
            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':  # 不更新account_id
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPContentTrend(**data)
                db.add(record)
                imported_count += 1
        
        return imported_count, updated_count
    
    @staticmethod
    def _import_content_source(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入内容流量来源明细"""
        imported_count = 0
        updated_count = 0
        
        for _, row in df.iterrows():
            channel = DataDetailsService._parse_str(row.get('传播渠道'))
            title = DataDetailsService._parse_str(row.get('内容标题'))
            publish_date = DataDetailsService._parse_date(row.get('发表日期'))
            
            if not channel or not title:
                continue
            
            # 检查是否已存在（基于渠道、标题和发表日期）
            existing = db.query(WeChatMPContentSource).filter(
                and_(
                    WeChatMPContentSource.account_id == account_id,
                    WeChatMPContentSource.channel == channel,
                    WeChatMPContentSource.title == title,
                    WeChatMPContentSource.publish_date == publish_date
                )
            ).first()
            
            data = {
                'account_id': account_id,
                'channel': channel,
                'publish_date': publish_date,
                'title': title,
                'read_count': DataDetailsService._parse_int(row.get('阅读次数')),
                'read_ratio': DataDetailsService._parse_str(row.get('阅读次数占比'))
            }
            
            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPContentSource(**data)
                db.add(record)
                imported_count += 1
        
        return imported_count, updated_count

    @staticmethod
    def _import_content_detail(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入内容已通知内容明细"""
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            title = DataDetailsService._parse_str(row.get('内容标题'))
            publish_time_value = row.get('发表时间')

            if not title:
                continue

            # 解析发表时间，支持多种格式包括8位数字格式
            publish_time = DataDetailsService._parse_datetime(publish_time_value)

            # 检查是否已存在（基于标题和发表时间）
            existing = db.query(WeChatMPContentDetail).filter(
                and_(
                    WeChatMPContentDetail.account_id == account_id,
                    WeChatMPContentDetail.title == title,
                    WeChatMPContentDetail.publish_time == publish_time
                )
            ).first()

            data = {
                'account_id': account_id,
                'title': title,
                'publish_time': publish_time,
                'total_read_user_count': DataDetailsService._parse_int(row.get('总阅读人数')),
                'total_read_count': DataDetailsService._parse_int(row.get('总阅读次数')),
                'total_share_user_count': DataDetailsService._parse_int(row.get('总分享人数')),
                'total_share_count': DataDetailsService._parse_int(row.get('总分享次数')),
                'follow_after_read_count': DataDetailsService._parse_int(row.get('阅读后关注人数')),
                'delivery_count': DataDetailsService._parse_int(row.get('送达人数')),
                'mp_message_read_count': DataDetailsService._parse_int(row.get('公众号消息阅读次数')),
                'delivery_read_rate': DataDetailsService._parse_str(row.get('送达阅读率')),
                'first_share_count': DataDetailsService._parse_int(row.get('首次分享次数')),
                'share_generated_read_count': DataDetailsService._parse_int(row.get('分享产生阅读次数')),
                'first_share_rate': DataDetailsService._parse_str(row.get('首次分享率')),
                'avg_read_per_share': DataDetailsService._parse_int(row.get('每次分享带来阅读次数')),
                'read_completion_rate': DataDetailsService._parse_str(row.get('阅读完成率')),
                'content_url': DataDetailsService._parse_str(row.get('内容url'))
            }

            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPContentDetail(**data)
                db.add(record)
                imported_count += 1

        return imported_count, updated_count

    @staticmethod
    def _import_user_channel(db: Session, account_id: int, df: pd.DataFrame) -> Tuple[int, int]:
        """导入用户增长明细"""
        imported_count = 0
        updated_count = 0

        for _, row in df.iterrows():
            date_value = DataDetailsService._parse_date(row.get('时间'))
            if not date_value:
                continue

            # 检查是否已存在
            existing = db.query(WeChatMPUserChannel).filter(
                and_(
                    WeChatMPUserChannel.account_id == account_id,
                    WeChatMPUserChannel.date == date_value
                )
            ).first()

            data = {
                'account_id': account_id,
                'date': date_value,
                'new_follow_count': DataDetailsService._parse_int(row.get('新关注人数')),
                'unfollow_count': DataDetailsService._parse_int(row.get('取消关注人数')),
                'net_follow_count': DataDetailsService._parse_int(row.get('净增关注人数')),
                'total_follow_count': DataDetailsService._parse_int(row.get('累积关注人数'))
            }

            if existing:
                # 更新现有记录
                for key, value in data.items():
                    if key != 'account_id':
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新记录
                record = WeChatMPUserChannel(**data)
                db.add(record)
                imported_count += 1

        return imported_count, updated_count

    @staticmethod
    def get_data_list(
        db: Session,
        account_id: Union[int, List[int], None],
        data_type: str,
        page: int = 1,
        page_size: int = 20,
        search: Optional[str] = None,
        sort_field: Optional[str] = None,
        sort_order: str = 'desc'
    ) -> Dict[str, Any]:
        """获取数据明细列表

        Args:
            account_id: 账号ID，可以是单个ID、ID列表或None（获取所有账号数据）
        """
        try:
            model_class = DataDetailsService.MODEL_MAPPING.get(data_type)
            if not model_class:
                return {"success": False, "error": f"不支持的数据类型: {data_type}"}

            # 构建查询条件
            if account_id is None:
                # 不过滤账号，获取所有数据
                query = db.query(model_class)
            elif isinstance(account_id, list):
                # 多个账号ID
                query = db.query(model_class).filter(model_class.account_id.in_(account_id))
            else:
                # 单个账号ID
                query = db.query(model_class).filter(model_class.account_id == account_id)

            # 添加搜索条件
            if search:
                if data_type == 'content_detail':
                    query = query.filter(model_class.title.contains(search))
                elif data_type == 'content_source':
                    query = query.filter(
                        or_(
                            model_class.title.contains(search),
                            model_class.channel.contains(search)
                        )
                    )

            # 添加排序
            if sort_field and hasattr(model_class, sort_field):
                sort_column = getattr(model_class, sort_field)
                if sort_order == 'asc':
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(desc(model_class.created_at))

            total = query.count()
            offset = (page - 1) * page_size
            items = query.offset(offset).limit(page_size).all()

            # 转换为字典格式
            data_list = []
            for item in items:
                item_dict = {}
                for column in model_class.__table__.columns:
                    value = getattr(item, column.name)
                    if isinstance(value, (date, datetime)):
                        value = value.isoformat()
                    item_dict[column.name] = value
                data_list.append(item_dict)

            return {
                "success": True,
                "data": data_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }

        except Exception as e:
            return {"success": False, "error": f"查询失败: {str(e)}"}

    @staticmethod
    def get_data_summary(db: Session, account_id: int, data_type: str) -> Dict[str, Any]:
        """获取数据汇总信息"""
        try:
            model_class = DataDetailsService.MODEL_MAPPING.get(data_type)
            if not model_class:
                return {"success": False, "error": f"不支持的数据类型: {data_type}"}

            total_records = db.query(model_class).filter(
                model_class.account_id == account_id
            ).count()

            latest_record = db.query(model_class).filter(
                model_class.account_id == account_id
            ).order_by(desc(model_class.created_at)).first()

            latest_time = None
            if latest_record:
                latest_time = latest_record.created_at.isoformat()

            return {
                "success": True,
                "total_records": total_records,
                "latest_time": latest_time
            }

        except Exception as e:
            return {"success": False, "error": f"获取汇总失败: {str(e)}"}
