from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.database import get_db
from app.routers.auth import get_current_user
from app.services.data_details_service import DataDetailsService
from app.models import PlatformAccount

router = APIRouter()

# Pydantic模型
class DataListResponse(BaseModel):
    success: bool
    data: list = []
    total: int = 0
    page: int = 1
    page_size: int = 20
    total_pages: int = 0
    error: Optional[str] = None

class DataSummaryResponse(BaseModel):
    success: bool
    total_records: int = 0
    latest_time: Optional[str] = None
    error: Optional[str] = None

@router.get("/wechat-mp/accounts")
async def get_wechat_mp_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户的微信公众号账号列表"""

    accounts = db.query(PlatformAccount).filter(
        PlatformAccount.user_id == current_user.id,
        (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
    ).all()

    account_list = []
    for account in accounts:
        account_list.append({
            "id": account.id,
            "name": account.name,
            "login_status": account.login_status,
            "last_login_time": account.last_login_time.isoformat() if account.last_login_time else None,
            "created_at": account.created_at.isoformat()
        })

    return {
        "success": True,
        "accounts": account_list
    }

@router.get("/wechat-mp/config")
async def get_wechat_mp_config():
    """获取微信公众号数据类型配置信息"""
    return {
        "success": True,
        "data_types": DATA_TYPE_CONFIG
    }

@router.get("/wechat-mp/{data_type}", response_model=DataListResponse)
async def get_wechat_mp_data_details(
    data_type: str,
    account_id: Optional[int] = Query(None, description="账号ID，不提供则显示所有账号数据"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_field: Optional[str] = Query(None, description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号数据明细列表

    支持的数据类型:
    - content_trend: 内容数据趋势明细
    - content_source: 内容流量来源明细
    - content_detail: 内容已通知内容明细
    - user_channel: 用户增长明细
    """

    # 验证数据类型
    valid_types = ['content_trend', 'content_source', 'content_detail', 'user_channel', 'user_source']
    if data_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的数据类型: {data_type}，支持的类型: {', '.join(valid_types)}"
        )

    # 验证账号权限
    if account_id is not None:
        # 验证特定账号权限
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id,
            (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
        ).first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )

        # 使用特定账号ID
        account_ids = [account_id]
    else:
        # 获取用户所有的微信公众号账号
        accounts = db.query(PlatformAccount).filter(
            PlatformAccount.user_id == current_user.id,
            (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
        ).all()

        if not accounts:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到任何微信公众号账号"
            )

        # 使用所有账号ID
        account_ids = [acc.id for acc in accounts]

    # 获取数据列表
    result = DataDetailsService.get_data_list(
        db=db,
        account_id=account_ids,
        data_type=data_type,
        page=page,
        page_size=page_size,
        search=search,
        sort_field=sort_field,
        sort_order=sort_order
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataListResponse(**result)

@router.get("/wechat-mp/{data_type}/summary", response_model=DataSummaryResponse)
async def get_wechat_mp_data_summary(
    data_type: str,
    account_id: int = Query(..., description="账号ID"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号数据汇总信息"""

    # 验证数据类型
    valid_types = ['content_trend', 'content_source', 'content_detail', 'user_channel', 'user_source']
    if data_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的数据类型: {data_type}"
        )

    # 验证账号权限
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id,
        (PlatformAccount.platform == "wechat_mp") | (PlatformAccount.platform == "wechat_service")
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="账号不存在或无权限访问"
        )

    # 获取汇总信息
    result = DataDetailsService.get_data_summary(
        db=db,
        account_id=account_id,
        data_type=data_type
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return DataSummaryResponse(**result)

@router.get("/wechat-mp/overview/account-summary")
async def get_wechat_mp_account_summary(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号账号数据汇总（最近4个周五的关注数）"""

    result = DataDetailsService.get_account_summary(db=db)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result

@router.get("/wechat-mp/overview/growth-summary")
async def get_wechat_mp_growth_summary(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号关注数合计净增长汇总"""

    result = DataDetailsService.get_growth_summary(db=db)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )

    return result

# 数据类型配置信息
DATA_TYPE_CONFIG = {
    "content_trend": {
        "name": "内容数据趋势明细",
        "description": "包含阅读次数、分享次数、收藏次数等趋势数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "date", "title": "日期", "type": "date"},
            {"key": "read_count", "title": "阅读次数", "type": "number"},
            {"key": "read_user_count", "title": "阅读人数", "type": "number"},
            {"key": "share_count", "title": "分享次数", "type": "number"},
            {"key": "share_user_count", "title": "分享人数", "type": "number"},
            {"key": "read_original_count", "title": "阅读原文次数", "type": "number"},
            {"key": "read_original_user_count", "title": "阅读原文人数", "type": "number"},
            {"key": "collect_count", "title": "收藏次数", "type": "number"},
            {"key": "collect_user_count", "title": "收藏人数", "type": "number"},
            {"key": "publish_count", "title": "群发篇数", "type": "number"},
            {"key": "channel", "title": "渠道", "type": "text"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "content_source": {
        "name": "内容流量来源明细",
        "description": "包含不同传播渠道的流量来源数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "channel", "title": "传播渠道", "type": "text"},
            {"key": "publish_date", "title": "发表日期", "type": "date"},
            {"key": "title", "title": "内容标题", "type": "text"},
            {"key": "read_count", "title": "阅读次数", "type": "number"},
            {"key": "read_ratio", "title": "阅读次数占比", "type": "text"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "content_detail": {
        "name": "内容已通知内容明细",
        "description": "包含具体文章的详细数据分析",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "title", "title": "内容标题", "type": "text"},
            {"key": "publish_time", "title": "发表时间", "type": "datetime"},
            {"key": "total_read_user_count", "title": "总阅读人数", "type": "number"},
            {"key": "total_read_count", "title": "总阅读次数", "type": "number"},
            {"key": "total_share_user_count", "title": "总分享人数", "type": "number"},
            {"key": "total_share_count", "title": "总分享次数", "type": "number"},
            {"key": "follow_after_read_count", "title": "阅读后关注人数", "type": "number"},
            {"key": "delivery_count", "title": "送达人数", "type": "number"},
            {"key": "mp_message_read_count", "title": "公众号消息阅读次数", "type": "number"},
            {"key": "delivery_read_rate", "title": "送达阅读率", "type": "text"},
            {"key": "first_share_count", "title": "首次分享次数", "type": "number"},
            {"key": "share_generated_read_count", "title": "分享产生阅读次数", "type": "number"},
            {"key": "first_share_rate", "title": "首次分享率", "type": "text"},
            {"key": "avg_read_per_share", "title": "每次分享带来阅读次数", "type": "number"},
            {"key": "read_completion_rate", "title": "阅读完成率", "type": "text"},
            {"key": "content_url", "title": "内容链接", "type": "url"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "user_channel": {
        "name": "用户增长明细",
        "description": "包含用户关注和取消关注的详细数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "date", "title": "时间", "type": "date"},
            {"key": "new_follow_count", "title": "新关注人数", "type": "number"},
            {"key": "unfollow_count", "title": "取消关注人数", "type": "number"},
            {"key": "net_follow_count", "title": "净增关注人数", "type": "number"},
            {"key": "total_follow_count", "title": "累积关注人数", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ]
    },
    "user_source": {
        "name": "用户来源明细",
        "description": "包含不同来源渠道的用户增长数据",
        "columns": [
            {"key": "account_name", "title": "账号名称", "type": "text"},
            {"key": "user_source", "title": "用户来源", "type": "user_source"},
            {"key": "date", "title": "日期", "type": "date"},
            {"key": "new_user", "title": "新用户", "type": "number"},
            {"key": "cancel_user", "title": "取消用户", "type": "number"},
            {"key": "netgain_user", "title": "净增用户", "type": "number"},
            {"key": "cumulate_user", "title": "累计用户", "type": "number"},
            {"key": "updated_at", "title": "更新时间", "type": "datetime"}
        ],
        "user_source_mapping": {
            "0": "其他合计",
            "1": "公众号搜索",
            "17": "名片分享",
            "30": "扫描二维码",
            "57": "文章内账号名称",
            "100": "微信广告",
            "161": "他人转载",
            "149": "小程序关注",
            "200": "视频号",
            "201": "直播"
        }
    }
}


