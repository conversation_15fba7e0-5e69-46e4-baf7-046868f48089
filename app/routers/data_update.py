from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.database import get_db
from app.routers.auth import get_current_user
from app.services.data_update_service import DataUpdateService
from app.models import DataUpdateRecord

router = APIRouter()

# Pydantic模型
class DataUpdateRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式

class DataUpdateResponse(BaseModel):
    success: bool
    task_id: Optional[int] = None
    total_accounts: Optional[int] = None
    message: Optional[str] = None
    error: Optional[str] = None

class UpdateStatusResponse(BaseModel):
    success: bool
    task_id: Optional[int] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    status: Optional[str] = None
    total_accounts: Optional[int] = None
    completed_accounts: Optional[int] = None
    current_account_name: Optional[str] = None
    current_step: Optional[str] = None
    error_message: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress_percent: Optional[float] = None
    error: Optional[str] = None

class DataRangeResponse(BaseModel):
    success: bool
    overall: Optional[dict] = None
    by_table: Optional[dict] = None
    error: Optional[str] = None

class UpdateHistoryResponse(BaseModel):
    success: bool
    data: list = []
    total: int = 0
    error: Optional[str] = None


@router.post("/start", response_model=DataUpdateResponse)
async def start_data_update(
    request: DataUpdateRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """启动数据更新任务"""
    
    result = await DataUpdateService.start_data_update(
        start_date=request.start_date,
        end_date=request.end_date
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return DataUpdateResponse(**result)


@router.get("/status/{task_id}", response_model=UpdateStatusResponse)
async def get_update_status(
    task_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取数据更新任务状态"""
    
    result = DataUpdateService.get_update_status(db=db, task_id=task_id)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["error"]
        )
    
    return UpdateStatusResponse(**result)


@router.get("/current-range", response_model=DataRangeResponse)
async def get_current_data_range(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前系统中的数据日期范围"""
    
    result = DataUpdateService.get_current_data_range(db=db)
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["error"]
        )
    
    return DataRangeResponse(**result)


@router.get("/history", response_model=UpdateHistoryResponse)
async def get_update_history(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取历史更新记录"""
    
    try:
        # 查询最近的20条更新记录
        records = db.query(DataUpdateRecord).order_by(
            DataUpdateRecord.created_at.desc()
        ).limit(20).all()
        
        data = []
        for record in records:
            data.append({
                "id": record.id,
                "start_date": record.start_date.isoformat(),
                "end_date": record.end_date.isoformat(),
                "status": record.status,
                "total_accounts": record.total_accounts,
                "completed_accounts": record.completed_accounts,
                "current_account_name": record.current_account_name,
                "current_step": record.current_step,
                "error_message": record.error_message,
                "created_at": record.created_at.isoformat(),
                "updated_at": record.updated_at.isoformat(),
                "completed_at": record.completed_at.isoformat() if record.completed_at else None,
                "progress_percent": round((record.completed_accounts / record.total_accounts) * 100, 1) if record.total_accounts > 0 else 0,
                "duration": None  # 可以计算任务耗时
            })
            
            # 计算任务耗时
            if record.completed_at and record.created_at:
                duration_seconds = (record.completed_at - record.created_at).total_seconds()
                data[-1]["duration"] = f"{int(duration_seconds // 60)}分{int(duration_seconds % 60)}秒"
        
        return UpdateHistoryResponse(
            success=True,
            data=data,
            total=len(data)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史记录失败: {str(e)}"
        )


@router.get("/running-task")
async def get_running_task(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前正在运行的任务"""
    
    try:
        running_task = db.query(DataUpdateRecord).filter(
            DataUpdateRecord.status == 'running'
        ).first()
        
        if not running_task:
            return {"success": True, "has_running_task": False}
        
        result = DataUpdateService.get_update_status(db=db, task_id=running_task.id)
        return {
            "success": True,
            "has_running_task": True,
            "task": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运行任务失败: {str(e)}"
        )
