from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, JSON, Float, Date, UniqueConstraint
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)

    accounts = relationship("PlatformAccount", back_populates="owner")
    feishu_apps = relationship("FeishuApp", back_populates="owner")

class FeishuApp(Base):
    __tablename__ = "feishu_apps"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))  # App名称
    app_id = Column(String(255))  # 飞书应用ID
    app_secret = Column(String(255))  # 飞书应用密钥
    user_id = Column(Integer, ForeignKey("users.id"))
    is_deleted = Column(Boolean, default=False)  # 删除标志
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 多维表格相关字段（一对一关系）
    bitable_name = Column(String(255))  # 多维表格名称
    app_token = Column(String(255))  # 多维表格token
    folder_token = Column(String(255))  # 文件夹token
    url = Column(String(500))  # 访问URL
    bitable_created_at = Column(DateTime)  # 多维表格创建时间

    owner = relationship("User", back_populates="feishu_apps")
    platform_accounts = relationship("PlatformAccount", back_populates="feishu_app")
    feishu_tables = relationship("FeishuTable", back_populates="feishu_app")



class PlatformAccount(Base):
    __tablename__ = "platform_accounts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))
    platform = Column(String(50))  # wechat_mp, wechat_service, xiaohongshu
    user_id = Column(Integer, ForeignKey("users.id"))
    login_status = Column(Boolean, default=False)
    last_login_time = Column(DateTime)
    cookies = Column(Text)  # 存储登录cookie
    created_at = Column(DateTime, default=datetime.utcnow)

    # 飞书应用关联
    feishu_app_id = Column(Integer, ForeignKey("feishu_apps.id"))  # 关联的飞书应用

    owner = relationship("User", back_populates="accounts")
    data_records = relationship("DataRecord", back_populates="account")
    feishu_tables = relationship("FeishuTable", back_populates="account")
    feishu_app = relationship("FeishuApp", back_populates="platform_accounts")

class DataRecord(Base):
    __tablename__ = "data_records"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    data_type = Column(String(50))  # user_summary, article_summary, etc.
    date = Column(DateTime)
    data = Column(JSON)  # 存储JSON格式的数据
    created_at = Column(DateTime, default=datetime.utcnow)

    account = relationship("PlatformAccount", back_populates="data_records")

class FeishuTable(Base):
    __tablename__ = "feishu_tables"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"))
    feishu_app_id = Column(Integer, ForeignKey("feishu_apps.id"))  # 关联的飞书应用
    feishu_table_id = Column(String(255))   # 数据表ID
    feishu_table_name = Column(String(255)) # 数据表名称
    data_type = Column(String(50))          # 数据类型：content_trend, content_source, content_detail, user_channel
    feishu_record_ids = Column(JSON)        # 该表的记录ID列表
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    account = relationship("PlatformAccount", back_populates="feishu_tables")
    feishu_app = relationship("FeishuApp", back_populates="feishu_tables")


# 微信公众号数据明细表
class WeChatMPContentTrend(Base):
    """微信公众号内容数据趋势明细表"""
    __tablename__ = "wechat_mp_content_trend"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    date = Column(Date, nullable=False)  # 日期
    read_count = Column(Integer, default=0)  # 阅读次数
    read_user_count = Column(Integer, default=0)  # 阅读人数
    share_count = Column(Integer, default=0)  # 分享次数
    share_user_count = Column(Integer, default=0)  # 分享人数
    read_original_count = Column(Integer, default=0)  # 阅读原文次数
    read_original_user_count = Column(Integer, default=0)  # 阅读原文人数
    collect_count = Column(Integer, default=0)  # 收藏次数
    collect_user_count = Column(Integer, default=0)  # 收藏人数
    publish_count = Column(Integer, default=0)  # 群发篇数
    channel = Column(String(100))  # 渠道
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    account = relationship("PlatformAccount")


class WeChatMPContentSource(Base):
    """微信公众号内容流量来源明细表"""
    __tablename__ = "wechat_mp_content_source"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    channel = Column(String(100))  # 传播渠道
    publish_date = Column(Date)  # 发表日期
    title = Column(Text)  # 内容标题
    read_count = Column(Integer, default=0)  # 阅读次数
    read_ratio = Column(String(20))  # 阅读次数占比
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    account = relationship("PlatformAccount")


class WeChatMPContentDetail(Base):
    """微信公众号内容已通知内容明细表"""
    __tablename__ = "wechat_mp_content_detail"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    title = Column(Text)  # 内容标题
    publish_time = Column(DateTime)  # 发表时间
    total_read_user_count = Column(Integer, default=0)  # 总阅读人数
    total_read_count = Column(Integer, default=0)  # 总阅读次数
    total_share_user_count = Column(Integer, default=0)  # 总分享人数
    total_share_count = Column(Integer, default=0)  # 总分享次数
    follow_after_read_count = Column(Integer, default=0)  # 阅读后关注人数
    delivery_count = Column(Integer, default=0)  # 送达人数
    mp_message_read_count = Column(Integer, default=0)  # 公众号消息阅读次数
    delivery_read_rate = Column(String(20))  # 送达阅读率
    first_share_count = Column(Integer, default=0)  # 首次分享次数
    share_generated_read_count = Column(Integer, default=0)  # 分享产生阅读次数
    first_share_rate = Column(String(20))  # 首次分享率
    avg_read_per_share = Column(Integer, default=0)  # 每次分享带来阅读次数
    read_completion_rate = Column(String(20))  # 阅读完成率
    content_url = Column(Text)  # 内容url
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    account = relationship("PlatformAccount")


class WeChatMPUserChannel(Base):
    """微信公众号用户增长表"""
    __tablename__ = "wechat_mp_user_channel"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    date = Column(Date, nullable=False)  # 时间
    new_follow_count = Column(Integer, default=0)  # 新关注人数
    unfollow_count = Column(Integer, default=0)  # 取消关注人数
    net_follow_count = Column(Integer, default=0)  # 净增关注人数
    total_follow_count = Column(Integer, default=0)  # 累积关注人数
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    account = relationship("PlatformAccount")


class WeChatMPUserSource(Base):
    """微信公众号用户来源数据表"""
    __tablename__ = "wechat_mp_user_source"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("platform_accounts.id"), nullable=False)
    user_source = Column(Integer, nullable=False)  # 用户来源类型
    date = Column(Date, nullable=False)  # 日期
    new_user = Column(Integer, default=0)  # 新用户数
    cancel_user = Column(Integer, default=0)  # 取消用户数
    netgain_user = Column(Integer, default=0)  # 净增用户数
    cumulate_user = Column(Integer, default=0)  # 累计用户数
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    account = relationship("PlatformAccount")

    # 添加唯一约束：同一账号、同一来源、同一日期只能有一条记录
    __table_args__ = (
        UniqueConstraint('account_id', 'user_source', 'date', name='uq_account_source_date'),
    )


class DataUpdateRecord(Base):
    __tablename__ = "data_update_records"

    id = Column(Integer, primary_key=True, index=True)
    start_date = Column(Date, nullable=False)  # 更新数据的开始日期
    end_date = Column(Date, nullable=False)    # 更新数据的结束日期
    status = Column(String(20), nullable=False, default='running')  # 'running', 'completed', 'failed'
    total_accounts = Column(Integer, default=0)  # 总账号数
    completed_accounts = Column(Integer, default=0)  # 已完成账号数
    current_account_name = Column(String(255))  # 当前处理的账号名
    current_step = Column(String(255))  # 当前执行步骤
    error_message = Column(Text)  # 错误信息
    created_at = Column(DateTime, default=datetime.utcnow)  # 创建时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)  # 更新时间
    completed_at = Column(DateTime)  # 完成时间