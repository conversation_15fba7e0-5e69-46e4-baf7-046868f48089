# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Test files
test/
tests/

# Documentation
docs/
*.md
!README.md

# User data (contains sensitive information)
user_data/

# Environment files
.env
.env.local
.env.production

# Node modules (will be installed in container)
frontend/node_modules/
frontend/build/
frontend/.env

# Cache
.cache/
