lxml-4.9.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
lxml-4.9.3.dist-info/LICENSE.txt,sha256=ae20RcEzWoMS1MCScYR-mVbYTw2fck0SU0DMP612eyo,1488
lxml-4.9.3.dist-info/LICENSES.txt,sha256=QdSd1AaqDhVIptXyGjDWv2OLPNlutyid00jYPtLkA5I,1514
lxml-4.9.3.dist-info/METADATA,sha256=ouTlpw5jKGmdXvYLiEPvyuKJ3h3f33OUTWg-H2UA7po,3755
lxml-4.9.3.dist-info/RECORD,,
lxml-4.9.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml-4.9.3.dist-info/WHEEL,sha256=nIJU2ngSxlMaDQl1pk3rfchh021IPsSvrkofb0YIYdE,115
lxml-4.9.3.dist-info/top_level.txt,sha256=NjD988wqaKq512nshNdLt-uDxsjkp4Bh51m6N-dhUrk,5
lxml/ElementInclude.py,sha256=PSLeZFvCa76WHJulPLxcZXJtCI2-4dK2CtqPRiYOAQg,8560
lxml/__init__.py,sha256=71f4Pgl0xksXqReX16a_Q4bf88TgXzKQnhEjGvLaQ10,575
lxml/__pycache__/ElementInclude.cpython-311.pyc,,
lxml/__pycache__/__init__.cpython-311.pyc,,
lxml/__pycache__/_elementpath.cpython-311.pyc,,
lxml/__pycache__/builder.cpython-311.pyc,,
lxml/__pycache__/cssselect.cpython-311.pyc,,
lxml/__pycache__/doctestcompare.cpython-311.pyc,,
lxml/__pycache__/pyclasslookup.cpython-311.pyc,,
lxml/__pycache__/sax.cpython-311.pyc,,
lxml/__pycache__/usedoctest.cpython-311.pyc,,
lxml/_elementpath.cpython-311-darwin.so,sha256=FDuYnr91KGfiucYyZxK-Q6SMbEMgBHOJ-xlQLIpuO3s,491663
lxml/_elementpath.py,sha256=wo6_CnGtKSkadI-krW8gbEZ1fVPTnIJINNwrdRfT_fw,10742
lxml/apihelpers.pxi,sha256=X9VHTgQGjf6F3AgG4BTHFqzoUWUclScGjileIvCMtFc,64452
lxml/builder.cpython-311-darwin.so,sha256=e8ahjagYjJL6Iq7qzibDRJ-1G0LIt1w6uHigS2-97pc,289722
lxml/builder.py,sha256=_eBVChPcD4XhLe4cXC3USsuV3tzNyHcLLykZnHPCfyg,8147
lxml/classlookup.pxi,sha256=AocnunTzIJiJE87ILWLy3FoiP7AiPoezGk-JJjrCQiM,22462
lxml/cleanup.pxi,sha256=L65WiKrJSOODfqMLVMMR8SFE4_BwjVHeudnPAUZcxNc,8458
lxml/cssselect.py,sha256=-4m3YmKAU-iXEJsnleNRQK4K7tPmAoRdhuikOqbUxBU,3364
lxml/debug.pxi,sha256=GorntYkTOs2Bm_HzI5ck3yPNPZAGzc3L37sKOiOSCOY,3283
lxml/docloader.pxi,sha256=DXwTMv2MR37LfVxtLmg9CFUMbHpuZ5X-AqXMF-sqzik,5783
lxml/doctestcompare.py,sha256=dAjqNzMGJuDsxY0xOXwOWzEsq7gSfQf-6uuxZZwaNXM,18339
lxml/dtd.pxi,sha256=-ytHvi8sDedEX2q9_3i3JbA1FUtSOhUzW3wpKKeUBZs,15219
lxml/etree.cpython-311-darwin.so,sha256=n9-Ru8ihfBGmrvV5FiaVj3aa4bQC2oDV_jV1n2b1KW8,10111896
lxml/etree.h,sha256=zH_dnQObaEbqPbLlrSjzkQU_f84P7NuBuCll5Psqads,8575
lxml/etree.pyx,sha256=Pu0C0c2DDHA33CWnPnxRYGx4pi0uF63JtzaCbAIVnNk,132415
lxml/etree_api.h,sha256=tgRCNFScrABi7OMWf9ouVJ33fW0Bd8j6mYMpEMsXqZU,17851
lxml/extensions.pxi,sha256=RGenDcaZ-tJiw_EJKk9BTUSZFhcPU2CQ_c8FHwVO9k0,33241
lxml/html/ElementSoup.py,sha256=s_dLobLMuKn2DhexR-iDXdZrMFg1RjLy1feHsIeZMpw,320
lxml/html/__init__.py,sha256=rbuJ5hfzlh37D9AMd7Srj6D17l9wL6Rub0-IUVGVx04,64937
lxml/html/__pycache__/ElementSoup.cpython-311.pyc,,
lxml/html/__pycache__/__init__.cpython-311.pyc,,
lxml/html/__pycache__/_diffcommand.cpython-311.pyc,,
lxml/html/__pycache__/_html5builder.cpython-311.pyc,,
lxml/html/__pycache__/_setmixin.cpython-311.pyc,,
lxml/html/__pycache__/builder.cpython-311.pyc,,
lxml/html/__pycache__/clean.cpython-311.pyc,,
lxml/html/__pycache__/defs.cpython-311.pyc,,
lxml/html/__pycache__/diff.cpython-311.pyc,,
lxml/html/__pycache__/formfill.cpython-311.pyc,,
lxml/html/__pycache__/html5parser.cpython-311.pyc,,
lxml/html/__pycache__/soupparser.cpython-311.pyc,,
lxml/html/__pycache__/usedoctest.cpython-311.pyc,,
lxml/html/_diffcommand.py,sha256=7-tz3udrgg0unGPAI8pa_uN4e7vW0MmgOXE43kKPdw8,2121
lxml/html/_html5builder.py,sha256=cASxN0Tks3_vqCA_sXa1oCx_McyRL6VpuRLA1T-B58o,3246
lxml/html/_setmixin.py,sha256=uVCgBUC4SJ7N9GotmlKHrhH7R4Kk7wGU3u1WmEJKGeM,1184
lxml/html/builder.py,sha256=aRgS-Ea9bli-muGX0iUQGKAe9D93P8BspQ2WPuiWJcU,4492
lxml/html/clean.cpython-311-darwin.so,sha256=95nLf9hpHSKh-SSgewYJVGbgVgHrOOpu9ywFtmk0g6I,670728
lxml/html/clean.py,sha256=LjnNRaN2xyc_m7Dd3XrDv9b_Anzv84pBUQQDUDsCK3U,28249
lxml/html/defs.py,sha256=ZzOp2TmY9f_ein9GIcDPyN8-f5HVptzSj56umimWub4,4236
lxml/html/diff.cpython-311-darwin.so,sha256=ON03YlKpvo2VB92PbcWxzTO32th2M355RD9ig1dAULQ,832071
lxml/html/diff.py,sha256=_juYjb3u9ZIZTyC2_Jl0zpzmIJccFQgr9fWTK4YpO3E,30553
lxml/html/formfill.py,sha256=9lnv7BnrQS0HOBY8ToeP1408xMN1wnltpsY-0CTGBpQ,9689
lxml/html/html5parser.py,sha256=dnyC4cqHxywjZSzk0mu2L7THTZjxhg4yF4pncjusa_w,8634
lxml/html/soupparser.py,sha256=tfdraMayPbMBCd2kGxUoTvNkhKUclfP3LmV9R85WKI4,10203
lxml/html/usedoctest.py,sha256=tPlmVz4KK1GRKV5DJLrdVECeqsT9PlDzSqqTodVi5s0,249
lxml/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__pycache__/__init__.cpython-311.pyc,,
lxml/includes/c14n.pxd,sha256=pGf910mVH9IdBb7r_aE-J59axIQcqFH4Sx_Tm0PA1m0,1123
lxml/includes/config.pxd,sha256=H6Mrl8It21hzRI2hzMId9W48QqkYYkoLT4dniLNmdTw,96
lxml/includes/dtdvalid.pxd,sha256=Rf2vRBbM4O1AOiIsUk_5M7pV3Dz309sS7Ccd2zGFHT0,671
lxml/includes/etree_defs.h,sha256=3EV64z9DyzIZG_21pSkzAI0F0bs_Zd7GQWbLC8H-8lI,15671
lxml/includes/etreepublic.pxd,sha256=3cdjIVlfkeZWYUav4y_T2uHwAo8yUCTlCvNLEvsZ_aI,10122
lxml/includes/extlibs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/extlibs/__pycache__/__init__.cpython-311.pyc,,
lxml/includes/extlibs/libcharset.h,sha256=GA0FumrbNI4VDGlzq3lf5CLaCwXgn4unw2l0btGQFwI,1510
lxml/includes/extlibs/localcharset.h,sha256=Z_AagaQeq0aDE7NPsVOqEf4nO4KcUp46ggo4d0ONIOQ,6338
lxml/includes/extlibs/zconf.h,sha256=8zPhnIxNk6he5b4bUXxrh1rz5BfVDJ4dELR_ZAYCjWw,16589
lxml/includes/extlibs/zlib.h,sha256=qYCg0QQZilPMIgxRq1hW5b6QG-yKLQLg7nmodUIZ3-0,97323
lxml/includes/htmlparser.pxd,sha256=Va2qbs5zVokERn57HbDY__CiBQOoCS4uI9wEfCnT6zk,2868
lxml/includes/libexslt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libexslt/__pycache__/__init__.cpython-311.pyc,,
lxml/includes/libexslt/exslt.h,sha256=eSW5tMJAewSUANLqk7AGEiU8b2BbCNRyauHnez7nKSU,3114
lxml/includes/libexslt/exsltconfig.h,sha256=I0m5Qut1Equ7peyWaseRI4jtPC5ENlOp4UthyUYI3QM,1172
lxml/includes/libexslt/exsltexports.h,sha256=1Jm9KTXm2FUUJIZ6V6-Uw55yG0BMULX3_goyxDd2LL8,1077
lxml/includes/libxml/HTMLparser.h,sha256=3LY9MRXs5ZCMbF91gTQol33ceRpambWVY0ciEPu6otI,9410
lxml/includes/libxml/HTMLtree.h,sha256=-i37-IqS_LU9jei_9yUi6Fs80ZErlrCGZWwaTHIg9p8,3646
lxml/includes/libxml/SAX.h,sha256=RAU0_E0-QfMjR6YQBQ09Ang53Dav9mh9tgffodsjR6g,4745
lxml/includes/libxml/SAX2.h,sha256=7N6Aq3v9PqZ-Sjg5nXvpT4PN7zITjOyK1todCcRbzG4,4742
lxml/includes/libxml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libxml/__pycache__/__init__.cpython-311.pyc,,
lxml/includes/libxml/c14n.h,sha256=MzaUhur3bsaqBSHPMmSEk-1ef4KJYJzGslYq2vuHz90,3117
lxml/includes/libxml/catalog.h,sha256=-XY3t2ZK0riJAJEHwqFBo4HamD0IH04M7Oq4lNqd0Gs,4906
lxml/includes/libxml/chvalid.h,sha256=H9_NIeLyDhdKDZg9euK_5b5QmeXWWNq4KTENFkcUUtA,5159
lxml/includes/libxml/debugXML.h,sha256=eatflryQfcmuAgmyz_f3JoSpTmvDA26mrcUjd7vwSLY,5152
lxml/includes/libxml/dict.h,sha256=N4yi-L-axuWDXJEa6GQPzms23bo08tqIKvpyoLbgb_c,1844
lxml/includes/libxml/encoding.h,sha256=PDMKE5wWovqByDJOM7O3jvh5csOjTQOH2qoyuA1BCiU,8046
lxml/includes/libxml/entities.h,sha256=ZnvIoxHdOd1EOKj7QNyvtbKOl8fQiU4iiDPjFrwFnAI,4742
lxml/includes/libxml/globals.h,sha256=zvj8n7X1-F8r88DKBhUwqrb3I1PCbWB5GrHoRsGK2sU,14427
lxml/includes/libxml/hash.h,sha256=rXg0ABwEyoGhmHT3ToW9Z-p-GH2W_UP1P7VarqUijCE,6601
lxml/includes/libxml/list.h,sha256=b7h42wGsDQTVWXsRutjNe1n00_zXfDIXr-iBOHvSThg,3348
lxml/includes/libxml/nanoftp.h,sha256=NcD6OVkAqfFA3C7IkGb6VWL3_xMdcIuKkO1ifnLg7lw,4140
lxml/includes/libxml/nanohttp.h,sha256=LLgN_JcupVO24_Oki895yVKsJzFe5mcF2vH8Ekymq_I,2005
lxml/includes/libxml/parser.h,sha256=jEvSywiF58dSru4rD0k_21hE5IiW86nTK7o8qTkGaOQ,39747
lxml/includes/libxml/parserInternals.h,sha256=d9gYX1xCoYk1TbKT1JLRSDpfuBjR0yak9BQNL2Tb20c,17599
lxml/includes/libxml/relaxng.h,sha256=VV0z7U85aRfd1zMLpShjMS21_g_HBCJmbMNgSKZvw58,6011
lxml/includes/libxml/schemasInternals.h,sha256=upFGK5WoLVjlIEavyqaPEn0c2zgb6IXZKDNSpll25rs,26224
lxml/includes/libxml/schematron.h,sha256=tSK9JnInoCONA6479YDubPyY1i0XCcesc5eFvhOAfxo,4370
lxml/includes/libxml/threads.h,sha256=WEglYa97yFtFdYnd8RQhHHx9OOqadhs0wyBbUOGhMRg,1988
lxml/includes/libxml/tree.h,sha256=_p4mIsJ1nsKnejbr-76FK6zaHqiOUOuEwM5cZjmvx5Q,38141
lxml/includes/libxml/uri.h,sha256=nGU1MVe2zr7e3jReDYKCMPQkTkQRN-iNlLewkpP9cLo,2664
lxml/includes/libxml/valid.h,sha256=bZidEfjKzKRE-mGloYqBgilXNAEqJ3KzDd1P2Wd2EiA,13645
lxml/includes/libxml/xinclude.h,sha256=iL_sqfaWIhThheHWBbEUQt59_sd-jnExhxaYeXxInqk,2967
lxml/includes/libxml/xlink.h,sha256=uIyzRPu6uszvGTopuf5pexJ__OI-0l4EPBZ4vhbsMaM,5042
lxml/includes/libxml/xmlIO.h,sha256=gkwMT2kiZnTG1-AzRkaSZX-4_BQkzAorYVGlsyyUDPw,10660
lxml/includes/libxml/xmlautomata.h,sha256=rIJxLWBGZrfkFYDXdJZBoAhygp93vFBO_WI6nUCxBN4,3956
lxml/includes/libxml/xmlerror.h,sha256=EI7eLBpmQXh-Vl1vlsSxqrGwJlgAMPCKqwIsPd2fqnI,36906
lxml/includes/libxml/xmlexports.h,sha256=3TXcVInGwrc7qFdDcy9HsP6afMj15TUiAr7NvcqaCkg,1339
lxml/includes/libxml/xmlmemory.h,sha256=oFCgcjrqhLx8DHVAhdoPk_sJcuDe7nQLOqn8GNuBcVU,5975
lxml/includes/libxml/xmlmodule.h,sha256=c5vXusZj46FsM9UPHGt8g9gdPhTVOH1rJEFf2pJ8K4c,1170
lxml/includes/libxml/xmlreader.h,sha256=mAAVz-ZYrOt56fzLds-ytusW7UqEGS5L9BGi5kzpTv4,12607
lxml/includes/libxml/xmlregexp.h,sha256=UHbZDD3CZVn4NDiaHB64Aa-90pxzSsconMqneCp1ZDc,5458
lxml/includes/libxml/xmlsave.h,sha256=8QlBKC6tuZ7lFQr_KJDTAxp4gmXtZWg-KGmLMkD7TCw,2337
lxml/includes/libxml/xmlschemas.h,sha256=6v9kS2Qty10RYWRCJYRoGbwR4DjcEUXiC5Flm-bXCjI,7068
lxml/includes/libxml/xmlschemastypes.h,sha256=66Ni3Gs-HXK0VdGWFxB5-Rri1PQeupxA-srM0HZlVpA,4856
lxml/includes/libxml/xmlstring.h,sha256=P_40FyEE40e_8h8Tlk9TSNze1zrJygFgsxkFydPnnPs,5511
lxml/includes/libxml/xmlunicode.h,sha256=TpTZ8Bf313Rs9m-ww1cBCHwvW-uHj9tQ125iQfaDorU,9993
lxml/includes/libxml/xmlversion.h,sha256=d6H_CGigEEogMH_A-JdxtaKMudc6zUfeaVeZsJHQVzQ,8420
lxml/includes/libxml/xmlwriter.h,sha256=ddjdRh9PBH-FynmNgjkRg1JFBRzlZBPyyh7FX3JX1lY,21265
lxml/includes/libxml/xpath.h,sha256=wU3jGXLU1tnojNoJPxP7L0pw-EcFXH3nTt_-nMcD558,16763
lxml/includes/libxml/xpathInternals.h,sha256=EWeEX1yCVHsUUeDxxOe-4jO25LBEDoM564CoPc1YNGU,19353
lxml/includes/libxml/xpointer.h,sha256=MYdvrCsDH05TfWXKYNPzcB7_aafylQYv4fhxFOdETMU,3784
lxml/includes/libxslt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libxslt/__pycache__/__init__.cpython-311.pyc,,
lxml/includes/libxslt/attributes.h,sha256=qKwzfGf7r89esLC65s96iYJWRA-s-Ezss2_V6Mmo1hk,957
lxml/includes/libxslt/documents.h,sha256=kBihgH5pqRvFalhm_fOFHtJTFhTpBcm681yT5dxgwfw,2704
lxml/includes/libxslt/extensions.h,sha256=W5UMyJqUP_1zt6sXZ0mgc0gAIwDJrZ8gjByhyrWqvd8,6899
lxml/includes/libxslt/extra.h,sha256=6X3Wu3NdPtrlqz-Koo7dB-rccnnszi6j3zg599gTByg,1640
lxml/includes/libxslt/functions.h,sha256=fc4CZj-9KeBHzO9-WWU_bNqmaEZAz3n7NNwClIBXk14,1972
lxml/includes/libxslt/imports.h,sha256=18kIjoGqdFXR63Ce3ZtzxsTiYV3XGKpchYakMUPDuUI,1840
lxml/includes/libxslt/keys.h,sha256=16v25VEluS7jYhgg6gYFwVxgGMn-1ctnlhhWWT4RcBY,1155
lxml/includes/libxslt/namespaces.h,sha256=VofSn2Kkn-a5JyRKCmY3jPp7amQy3n09vzy0KUQt4q0,1666
lxml/includes/libxslt/numbersInternals.h,sha256=Eg5gYZ5p3h0_e5wyI61S-0E6_ArVJzv0yr63j6BU2fc,2019
lxml/includes/libxslt/pattern.h,sha256=tJ-BPfs9UYgiZMMoQZbhij3g7xVppYq7TrrOu25eR7Q,2110
lxml/includes/libxslt/preproc.h,sha256=D_LjEdHhsdyBnEAvflnwFgoR4hGUb72kgEhXkkmPRsw,896
lxml/includes/libxslt/security.h,sha256=fUD1cy_WxFCTvTNAF0WOQIU4p5CNWn1LHFyZJd-Fx5U,2652
lxml/includes/libxslt/templates.h,sha256=bnt6Jqui6KU5pNUdMNPbQZkZ5d-VTWqC0TMGkOlVoIo,2268
lxml/includes/libxslt/transform.h,sha256=ICT7meUV0OTAx27WaKVrKj-aUmR9LSpTNaOAJd2UStg,6311
lxml/includes/libxslt/variables.h,sha256=cQAgPe4QCcK2uKbWg7Iz-9peM9xWGm7m3M6jQm0sjIA,3143
lxml/includes/libxslt/xslt.h,sha256=wmFx2Q31Pd8Iq2phAQpY9J3QQatb8lWg3gABtqKFgEw,1964
lxml/includes/libxslt/xsltInternals.h,sha256=VKpvlPx4gUMYYdnaTFgyzcwamcGR0oOUou0iQi8-I9Q,57894
lxml/includes/libxslt/xsltconfig.h,sha256=8essC41EytXK9AK7LveEUo9dGYJ06bnkn__3c3hSWes,3702
lxml/includes/libxslt/xsltexports.h,sha256=1-luH-0bCIgBAlKAXhV-dqHBfwOAQNDamiYbxIlTf0k,1124
lxml/includes/libxslt/xsltlocale.h,sha256=HDeTbUBXCje5VqVUktsALgyFRVBbwIcVW15f8wftAag,873
lxml/includes/libxslt/xsltutils.h,sha256=1eguYgR9-jeNOVlBUktHboaq-VLX6JXraO80TfbARKM,9085
lxml/includes/lxml-version.h,sha256=eiP5zGtOCiw2fc3tjHNZR2Z_7TPJWwtziHVwuMTK858,71
lxml/includes/relaxng.pxd,sha256=12yapjqDZLF_HTlcuSXSoQpPGK1NU7fj7gzS1EF8kZw,2669
lxml/includes/schematron.pxd,sha256=5_PUpLHTzzYZ_d-8d2OjKLdwtLIdOm7C20HFUAX8hD4,1640
lxml/includes/tree.pxd,sha256=dtnXNbEfxV3-5kOwWYkYoCE8HT5zvFVNdKFaIBuNXBc,20091
lxml/includes/uri.pxd,sha256=5wPtpGU1JtdmpZMTzR8EswazihP3dxkns6Fgo9NWOt8,139
lxml/includes/xinclude.pxd,sha256=onXD71LVdAbXjUj82_SDtSixNsNh8xbu6Nd9x0V3bmM,852
lxml/includes/xmlerror.pxd,sha256=JDY_9OKoW5nue7wFe1xft0vk1kmAzo6nVC5DmhKXWI0,58004
lxml/includes/xmlparser.pxd,sha256=X6ab9_HX8gKoQVgdLy9tdsFEdYfayihV5dDhQ9LgGmA,10869
lxml/includes/xmlschema.pxd,sha256=yYQFrIKAQ_feenENV24X2AZyBIYGBltRDm9qB7CYMww,1696
lxml/includes/xpath.pxd,sha256=tKYAcwpbSRq8qrsZ2ISVYvEaLnCV9GadNC5o_f8Ua_g,5794
lxml/includes/xslt.pxd,sha256=qBU-0dLhIMQFv58I4q1XkBq9QJpJzKEAK9qBFPXBj_g,8341
lxml/isoschematron/__init__.py,sha256=NUoI0bb87VB2XePzFcMtwUasrUAQACfb3Z9dPaMz6Fs,12399
lxml/isoschematron/__pycache__/__init__.cpython-311.pyc,,
lxml/isoschematron/resources/rng/iso-schematron.rng,sha256=VsWxPyi3iViJDDbjJJw0wWkEHkLrz9zoCA8zJLor9N4,18337
lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl,sha256=ObebsB8Wt-d3uIA_U5NU85TpnQ3PxPX38TdOAqosMac,3172
lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl,sha256=QweRrIIM-zFcgg98GXA2CaWfIbgVE0XKEeYSfvv67A0,4563
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl,sha256=xSZ_Ekq_I-62ZpiE5AqYYHwFW_qh855zt9V4_s7rbkY,11703
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl,sha256=x42QJ-dxQ1waPzydsCoQnp2Xj15y53nW43O7BuoDRHk,39957
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl,sha256=Tr9BnO6pzjVWwhqJfm10UlvAy95EgfSCz2iMlrVGT6Q,2015
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl,sha256=ue8q_88X4e_jsJizo31GRNBxNhdxkEE9fY20oq0Iqwk,71764
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl,sha256=BBAdsVSi5zAzeGepuN6gS1saQINDqITXKplmmj4dTWg,20382
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt,sha256=OGLiFswuLJEW5EPYKOeoauuCJFEtVa6jyzBE1OcJI98,3310
lxml/iterparse.pxi,sha256=uYVCe5hRTf1MP6MdFD4nwvC_LhHXt4CaXkfigJMdHEU,16607
lxml/lxml.etree.h,sha256=zH_dnQObaEbqPbLlrSjzkQU_f84P7NuBuCll5Psqads,8575
lxml/lxml.etree_api.h,sha256=kkRNu4Gut2eFWdRGW2SEm_-1eKZERFGYlNNaHIyQYTc,17856
lxml/nsclasses.pxi,sha256=84Y3KIXCUQNl64e5Cg0Bcqaq2jzCB8Mmndn07oY3osQ,9145
lxml/objectify.cpython-311-darwin.so,sha256=jfvpWLTCFO0oRTX0a9j1xtODecomInTRM_mRPNRmmqA,5733772
lxml/objectify.pyx,sha256=B8qHvDa2VWlU4Z9K3vaCCewjfZpvFNNqKH4E3fuP10Y,77053
lxml/objectpath.pxi,sha256=32MJBiv1lOYCZpom_2oAW27yNauWrG7ym7_5SJEPk9U,11479
lxml/parser.pxi,sha256=Nhjp3bkJcDav2cu4eEW-4SlGnnRQyiEbSh-La43vOx4,78247
lxml/parsertarget.pxi,sha256=THBYdmmRYCoj68BEv-BMXR3_NI8ME9OWVJBdypAhuQY,6859
lxml/proxy.pxi,sha256=Tna5v8R1eJ50mJbbBHHFEUr2ndiQo8eV1pzsYzXhj-c,23562
lxml/public-api.pxi,sha256=LYxBK_CXxI7NLzzSCXQaNqRjpUhWuD1o4fK-W_NxVvQ,6660
lxml/pyclasslookup.py,sha256=gLD1HM2HtITYYiGzjEOewSwbB7XkVx_NZv_quCt79Oc,92
lxml/readonlytree.pxi,sha256=jIvwD-ctSm7WLK5yAPlev_xGddpCouqi-OpinOIP0RM,19048
lxml/relaxng.pxi,sha256=xQWG3HsUEzTQIis9MUKDeOq25jOJJm3X1CgOWkQfEZ4,6085
lxml/sax.cpython-311-darwin.so,sha256=Bz3GjrIN4_BpEByrctRYJRL7rmoSWhHDXWmRAUIRPqk,429574
lxml/sax.py,sha256=xcs8RDKFWnqCbZpYdDuJPLIUTbThrCYnX9arDhxlgOY,9396
lxml/saxparser.pxi,sha256=0R_0Pi0F9Y_1IWuJ_1AhaMp_I4kkubsovk1bt1RSRYc,32542
lxml/schematron.pxi,sha256=6ZvkG5fDcBLYEdwSaXplwcgbp8eiBexEo6UZ6Tl-sug,5782
lxml/serializer.pxi,sha256=Y0k1Knswc5bNkd7Qv094K5bfD_QyG-IYyWbhi6xRYqY,67999
lxml/usedoctest.py,sha256=qRgZKQVcAZcl-zN0AIXVJnOsETUXz2nPXkxuzs1lGgk,230
lxml/xinclude.pxi,sha256=LPL_U0dx-qLP2slshTaOkIObeI8q35BsTU-eViuNn9A,2460
lxml/xmlerror.pxi,sha256=v0ky0Gp9dnwjoMcP9wZ3ukuyjDzRHi2huwdFC3v9sx4,49531
lxml/xmlid.pxi,sha256=v7fXygmofM83LHmM7aZMELNkqIBvIRLc35gABWICfAE,6064
lxml/xmlschema.pxi,sha256=FmDRpaEJXNmNV57nWnZlK_xKyqZt2rz2OLs3PO4iKhI,8079
lxml/xpath.pxi,sha256=4-Su_FRyrBuj0GUcneqJcuFX8O0cjTlJqrztZBazgoY,19571
lxml/xslt.pxi,sha256=-vxp7JNgRRLsupTDcZt4JIvIp1xpGPYYECv0BbR3BOg,36694
lxml/xsltext.pxi,sha256=UJbJF6ZZrbgbBWDgDMUh5MpbNhEMEZ8kWqea_TLfZzA,11085
